{"name": "mobile-spo-backend", "version": "1.0.0", "description": "Privacy-first medical AI backend for Mobile Spo health app", "main": "src/server.js", "type": "module", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "test": "jest", "lint": "eslint src/", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "mongoose": "^8.0.3", "socket.io": "^4.7.4", "winston": "^3.11.0", "crypto": "^1.0.1", "@tensorflow/tfjs-node": "^4.15.0", "@huggingface/inference": "^2.6.4", "node-cron": "^3.0.3", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "keywords": ["medical-ai", "healthcare", "privacy", "mental-health", "telemedicine"], "author": "Mobile Spo Team", "license": "MIT"}