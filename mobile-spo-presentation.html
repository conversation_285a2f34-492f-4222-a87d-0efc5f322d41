<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Spo - Revolutionizing Healthcare Access in South Africa</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        .presentation {
            scroll-behavior: smooth;
        }

        .slide {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .slide-content {
            max-width: 1200px;
            width: 100%;
            text-align: center;
            z-index: 2;
            position: relative;
        }

        /* Slide Backgrounds */
        .slide-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .slide-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
        .slide-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }
        .slide-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; }
        .slide-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; }
        .slide-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; }
        .slide-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333; }
        .slide-8 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }

        /* Typography */
        .slide h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: slideInDown 1s ease-out;
        }

        .slide h2 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            animation: slideInLeft 1s ease-out 0.3s both;
        }

        .slide h3 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2563eb;
        }

        .slide p {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .subtitle {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            animation: slideInRight 1s ease-out 0.5s both;
        }

        /* Logo and Icons */
        .logo {
            font-size: 6rem;
            margin-bottom: 1rem;
            animation: bounceIn 1.5s ease-out;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: inline-block;
            animation: pulse 2s infinite;
        }

        /* Grid Layouts */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: fadeInUp 1s ease-out;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .problem-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .problem-item {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 5px solid #ff6b6b;
            animation: slideInLeft 1s ease-out;
        }

        /* Stats */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            animation: countUp 2s ease-out;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #ffd700;
            display: block;
            margin-bottom: 0.5rem;
        }

        /* Emergency Section */
        .emergency-section {
            background: rgba(220, 38, 38, 0.1);
            border: 2px solid #dc2626;
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            animation: pulse 3s infinite;
        }

        .emergency-contacts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .emergency-contact {
            background: rgba(220, 38, 38, 0.2);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }

        .contact-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #dc2626;
        }

        /* Navigation */
        .nav-dots {
            position: fixed;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            margin: 0.5rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: white;
            transform: scale(1.5);
        }

        /* Buttons */
        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.2rem;
            margin: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: bounceIn 1s ease-out 1s both;
        }

        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.4);
        }

        /* Animations */
        @keyframes slideInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes countUp {
            from { opacity: 0; transform: scale(0.5); }
            to { opacity: 1; transform: scale(1); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .slide h1 { font-size: 2.5rem; }
            .slide h2 { font-size: 2rem; }
            .slide p { font-size: 1rem; }
            .logo { font-size: 4rem; }
            .icon { font-size: 3rem; }
            .features-grid { grid-template-columns: 1fr; }
            .nav-dots { display: none; }
        }

        /* Background Animations */
        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- Navigation Dots -->
        <div class="nav-dots">
            <div class="nav-dot active" onclick="scrollToSlide(1)"></div>
            <div class="nav-dot" onclick="scrollToSlide(2)"></div>
            <div class="nav-dot" onclick="scrollToSlide(3)"></div>
            <div class="nav-dot" onclick="scrollToSlide(4)"></div>
            <div class="nav-dot" onclick="scrollToSlide(5)"></div>
            <div class="nav-dot" onclick="scrollToSlide(6)"></div>
            <div class="nav-dot" onclick="scrollToSlide(7)"></div>
            <div class="nav-dot" onclick="scrollToSlide(8)"></div>
        </div>

        <!-- Slide 1: Title -->
        <section class="slide slide-1" id="slide-1">
            <div class="slide-content">
                <div class="logo">🏥</div>
                <h1>Mobile Spo</h1>
                <p class="subtitle">Revolutionizing Healthcare Access in South Africa</p>
                <p style="font-size: 1.3rem; margin-bottom: 2rem;">Privacy-First AI Health Assistant for 60 Million South Africans</p>
                <a href="#slide-2" class="cta-button">Discover Our Mission</a>
            </div>
        </section>

        <!-- Slide 2: The Problem -->
        <section class="slide slide-2" id="slide-2">
            <div class="slide-content">
                <div class="icon">⚠️</div>
                <h2>The Healthcare Crisis We're Solving</h2>
                <div class="problem-grid">
                    <div class="problem-item">
                        <h3>🌍 Geographic Barriers</h3>
                        <p>Rural communities travel hours to reach healthcare facilities</p>
                    </div>
                    <div class="problem-item">
                        <h3>🗣️ Language Barriers</h3>
                        <p>Medical information unavailable in isiZulu, Sesotho, and other local languages</p>
                    </div>
                    <div class="problem-item">
                        <h3>💰 Economic Barriers</h3>
                        <p>Many cannot afford regular doctor visits or transportation</p>
                    </div>
                    <div class="problem-item">
                        <h3>🧠 Mental Health Crisis</h3>
                        <p>Limited access to mental health support and crisis intervention</p>
                    </div>
                    <div class="problem-item">
                        <h3>📱 Digital Divide</h3>
                        <p>45 million South Africans still use feature phones, not smartphones</p>
                    </div>
                    <div class="problem-item">
                        <h3>⏰ Healthcare Delays</h3>
                        <p>Long waiting times and overcrowded public healthcare facilities</p>
                    </div>
                </div>
                <a href="#slide-3" class="cta-button">See Our Solution</a>
            </div>
        </section>

        <!-- Slide 3: Our Solution -->
        <section class="slide slide-3" id="slide-3">
            <div class="slide-content">
                <div class="icon">💡</div>
                <h2>Multi-Platform Health Ecosystem</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📱</div>
                        <h3>Mobile App</h3>
                        <p>AI Health Assistant with symptom tracking, community support, and crisis intervention</p>
                        <ul style="text-align: left; margin-top: 1rem;">
                            <li>✅ Instant medical guidance</li>
                            <li>✅ Daily health journaling</li>
                            <li>✅ Peer-to-peer support</li>
                            <li>✅ Appointment booking</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📞</div>
                        <h3>USSD Integration</h3>
                        <p>Universal access for feature phones using simple *123# codes</p>
                        <ul style="text-align: left; margin-top: 1rem;">
                            <li>✅ Works on any phone</li>
                            <li>✅ Voice integration</li>
                            <li>✅ SMS emergency alerts</li>
                            <li>✅ Offline capable</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">🔒</div>
                        <h3>Privacy-First</h3>
                        <p>All AI processing happens locally - no personal data sent to cloud</p>
                        <ul style="text-align: left; margin-top: 1rem;">
                            <li>✅ Local AI processing</li>
                            <li>✅ GDPR compliant</li>
                            <li>✅ Encrypted storage</li>
                            <li>✅ Data sovereignty</li>
                        </ul>
                    </div>
                </div>
                <a href="#slide-4" class="cta-button">Life-Saving Features</a>
            </div>
        </section>

        <!-- Slide 4: Crisis Intervention -->
        <section class="slide slide-4" id="slide-4">
            <div class="slide-content">
                <div class="icon">🚨</div>
                <h2>Life-Saving Crisis Intervention</h2>
                <div class="emergency-section">
                    <h3 style="color: #dc2626; font-size: 2rem; margin-bottom: 1rem;">Real-Time Emergency Detection</h3>
                    <p style="font-size: 1.3rem; margin-bottom: 2rem;">AI identifies suicidal ideation, self-harm, and domestic violence in real-time</p>

                    <div class="emergency-contacts">
                        <div class="emergency-contact">
                            <div style="font-size: 2rem;">📞</div>
                            <div class="contact-number">0800567567</div>
                            <p>Crisis Helpline</p>
                        </div>
                        <div class="emergency-contact">
                            <div style="font-size: 2rem;">🆘</div>
                            <div class="contact-number">0800121314</div>
                            <p>Suicide Prevention</p>
                        </div>
                        <div class="emergency-contact">
                            <div style="font-size: 2rem;">🚑</div>
                            <div class="contact-number">10177</div>
                            <p>Emergency Services</p>
                        </div>
                        <div class="emergency-contact">
                            <div style="font-size: 2rem;">💬</div>
                            <div class="contact-number">31393</div>
                            <p>SMS Counseling</p>
                        </div>
                    </div>
                </div>
                <p style="font-size: 1.2rem; margin-top: 2rem; font-weight: 600;">Immediate connection to local South African crisis resources</p>
                <a href="#slide-5" class="cta-button">Market Opportunity</a>
            </div>
        </section>

        <!-- Slide 5: Market Opportunity -->
        <section class="slide slide-5" id="slide-5">
            <div class="slide-content">
                <div class="icon">📊</div>
                <h2>Massive Market Opportunity</h2>
                <div class="stats-container">
                    <div class="stat-item">
                        <span class="stat-number">60M</span>
                        <p>People with mobile phones</p>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">45M</span>
                        <p>Using feature phones (USSD opportunity)</p>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">R500B</span>
                        <p>Healthcare market size</p>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <p>Mobile phone penetration</p>
                    </div>
                </div>

                <div style="background: rgba(255,255,255,0.1); border-radius: 20px; padding: 2rem; margin: 2rem 0;">
                    <h3 style="color: #ffd700; margin-bottom: 1rem;">Freemium Monetization Strategy</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                        <div>
                            <h4>Free Tier</h4>
                            <p>Basic AI chat, emergency features, community access</p>
                        </div>
                        <div>
                            <h4>Premium (R50/month)</h4>
                            <p>Advanced tracking, priority support, telehealth</p>
                        </div>
                        <div>
                            <h4>B2B Partnerships</h4>
                            <p>Healthcare providers, government contracts</p>
                        </div>
                        <div>
                            <h4>Corporate Wellness</h4>
                            <p>Employee health programs</p>
                        </div>
                    </div>
                </div>
                <a href="#slide-6" class="cta-button">Our Impact Goals</a>
            </div>
        </section>

        <!-- Slide 6: Impact Goals -->
        <section class="slide slide-6" id="slide-6">
            <div class="slide-content">
                <div class="icon">🎯</div>
                <h2 style="color: #2563eb;">Target Impact & Growth</h2>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin: 2rem 0;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px; padding: 2rem;">
                        <h3 style="color: #ffd700; margin-bottom: 1rem;">Year 1 Goals</h3>
                        <div style="text-align: left;">
                            <p>✅ <strong>100,000 users</strong> across urban and rural areas</p>
                            <p>✅ <strong>50 healthcare partnerships</strong> for referrals</p>
                            <p>✅ <strong>24/7 crisis support</strong> preventing suicides</p>
                            <p>✅ <strong>Multi-language expansion</strong> to all 11 official languages</p>
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; border-radius: 20px; padding: 2rem;">
                        <h3 style="color: #fff; margin-bottom: 1rem;">Long-term Vision</h3>
                        <div style="text-align: left;">
                            <p>🚀 <strong>National deployment</strong> across all provinces</p>
                            <p>🚀 <strong>Public healthcare integration</strong></p>
                            <p>🚀 <strong>AI-powered population health</strong> insights</p>
                            <p>🚀 <strong>Export to African countries</strong></p>
                        </div>
                    </div>
                </div>

                <div style="background: rgba(37, 99, 235, 0.1); border-radius: 20px; padding: 2rem; margin: 2rem 0;">
                    <h3 style="color: #2563eb; margin-bottom: 1rem;">Cultural Sensitivity & Innovation</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <div>
                            <h4>🌍 Multi-Language</h4>
                            <p>English, isiZulu, Sesotho, French</p>
                        </div>
                        <div>
                            <h4>🏥 Local Context</h4>
                            <p>SA healthcare system integration</p>
                        </div>
                        <div>
                            <h4>👥 Community-Driven</h4>
                            <p>Peer support networks</p>
                        </div>
                        <div>
                            <h4>📱 Universal Access</h4>
                            <p>Smartphone + feature phone support</p>
                        </div>
                    </div>
                </div>
                <a href="#slide-7" class="cta-button">Why We'll Win</a>
            </div>
        </section>

        <!-- Slide 7: Competitive Advantage -->
        <section class="slide slide-7" id="slide-7">
            <div class="slide-content">
                <div class="icon">🏆</div>
                <h2 style="color: #2563eb;">Why Mobile Spo Will Succeed</h2>

                <div class="features-grid">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px; padding: 2rem;">
                        <h3 style="color: #ffd700;">✅ Proven Technology</h3>
                        <ul style="text-align: left; margin-top: 1rem;">
                            <li>Working AI with emergency detection</li>
                            <li>Cross-platform React Native + USSD</li>
                            <li>Privacy-first local processing</li>
                            <li>Real-time crisis intervention</li>
                        </ul>
                    </div>

                    <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; border-radius: 20px; padding: 2rem;">
                        <h3 style="color: #fff;">✅ Market Validation</h3>
                        <ul style="text-align: left; margin-top: 1rem;">
                            <li>Healthcare access is critical national issue</li>
                            <li>Underserved rural communities need solutions</li>
                            <li>Government NHI alignment</li>
                            <li>Growing digital health demand</li>
                        </ul>
                    </div>

                    <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; border-radius: 20px; padding: 2rem;">
                        <h3 style="color: #fff;">✅ Unique Advantages</h3>
                        <ul style="text-align: left; margin-top: 1rem;">
                            <li><strong>ONLY</strong> health app with USSD integration</li>
                            <li>Local languages & cultural sensitivity</li>
                            <li>Life-saving emergency detection</li>
                            <li>Privacy-first data sovereignty</li>
                        </ul>
                    </div>
                </div>

                <div style="background: rgba(220, 38, 38, 0.1); border: 2px solid #dc2626; border-radius: 20px; padding: 2rem; margin: 2rem 0;">
                    <h3 style="color: #dc2626; margin-bottom: 1rem;">🚨 This Isn't Just Business - It's Life or Death</h3>
                    <p style="font-size: 1.3rem; font-weight: 600;">Every day we delay, South Africans in crisis can't access help. Our emergency detection system can literally save lives.</p>
                </div>
                <a href="#slide-8" class="cta-button">Join Our Mission</a>
            </div>
        </section>

        <!-- Slide 8: Call to Action -->
        <section class="slide slide-8" id="slide-8">
            <div class="slide-content">
                <div class="logo">🇿🇦</div>
                <h1 style="font-size: 3rem;">Join the Healthcare Revolution</h1>
                <p class="subtitle">Mobile Spo: Democratizing Healthcare for 60 Million South Africans</p>

                <div style="background: rgba(255,255,255,0.1); border-radius: 20px; padding: 2rem; margin: 2rem 0; backdrop-filter: blur(10px);">
                    <h3 style="color: #ffd700; margin-bottom: 1rem;">We're Not Just Building an App</h3>
                    <p style="font-size: 1.4rem; font-weight: 600; margin-bottom: 1rem;">We're Building Healthcare Infrastructure for the Future</p>
                    <p style="font-size: 1.2rem;">Every South African deserves access to quality health information and crisis support, regardless of location, language, or economic status.</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin: 2rem 0;">
                    <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 1.5rem; backdrop-filter: blur(10px);">
                        <h4 style="color: #ffd700;">🚀 Ready to Launch</h4>
                        <p>Working prototype with AI, emergency detection, and multi-platform support</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 1.5rem; backdrop-filter: blur(10px);">
                        <h4 style="color: #ffd700;">💡 Proven Innovation</h4>
                        <p>First health platform combining smartphone apps with USSD for universal access</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 1.5rem; backdrop-filter: blur(10px);">
                        <h4 style="color: #ffd700;">❤️ Life-Saving Impact</h4>
                        <p>Real-time crisis intervention connecting people to local South African resources</p>
                    </div>
                </div>

                <div style="margin: 3rem 0;">
                    <a href="http://localhost:8081" class="cta-button" style="font-size: 1.5rem; padding: 1.5rem 3rem;">🚀 Try Live Demo</a>
                    <a href="http://localhost:3001/health" class="cta-button" style="font-size: 1.5rem; padding: 1.5rem 3rem;">🏥 Test Backend API</a>
                </div>

                <p style="font-size: 1.3rem; font-weight: 600; margin-top: 2rem;">
                    Together, we can revolutionize healthcare for 60 million South Africans 🇿🇦
                </p>

                <div style="margin-top: 2rem; font-size: 1rem; opacity: 0.8;">
                    <p>Mobile Spo - Privacy-First AI Health Assistant</p>
                    <p>Built with ❤️ for South Africa</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Navigation functionality
        function scrollToSlide(slideNumber) {
            const slide = document.getElementById(`slide-${slideNumber}`);
            slide.scrollIntoView({ behavior: 'smooth' });

            // Update active dot
            document.querySelectorAll('.nav-dot').forEach((dot, index) => {
                dot.classList.toggle('active', index === slideNumber - 1);
            });
        }

        // Auto-update navigation dots on scroll
        window.addEventListener('scroll', () => {
            const slides = document.querySelectorAll('.slide');
            const scrollPosition = window.scrollY + window.innerHeight / 2;

            slides.forEach((slide, index) => {
                const slideTop = slide.offsetTop;
                const slideBottom = slideTop + slide.offsetHeight;

                if (scrollPosition >= slideTop && scrollPosition < slideBottom) {
                    document.querySelectorAll('.nav-dot').forEach((dot, dotIndex) => {
                        dot.classList.toggle('active', dotIndex === index);
                    });
                }
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            const currentSlide = document.querySelector('.nav-dot.active');
            const currentIndex = Array.from(document.querySelectorAll('.nav-dot')).indexOf(currentSlide);

            if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
                const nextIndex = Math.min(currentIndex + 1, 7);
                scrollToSlide(nextIndex + 1);
            } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                const prevIndex = Math.max(currentIndex - 1, 0);
                scrollToSlide(prevIndex + 1);
            }
        });

        // Add entrance animations on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.feature-card, .problem-item, .stat-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add click handlers for smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Add some interactive effects
        document.querySelectorAll('.feature-card, .stat-item').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate numbers in stats
        function animateNumbers() {
            document.querySelectorAll('.stat-number').forEach(stat => {
                const text = stat.textContent;
                if (text.includes('M') || text.includes('B')) {
                    const number = parseInt(text);
                    let current = 0;
                    const increment = number / 50;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= number) {
                            current = number;
                            clearInterval(timer);
                        }
                        stat.textContent = Math.floor(current) + text.slice(-1);
                    }, 30);
                }
            });
        }

        // Trigger number animation when stats section is visible
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    statsObserver.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.stats-container');
        if (statsSection) {
            statsObserver.observe(statsSection);
        }
    </script>
</body>
</html>
