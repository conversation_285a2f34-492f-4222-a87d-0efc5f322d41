# Server Configuration
PORT=3001
NODE_ENV=development
API_VERSION=v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/mobile_spo_health
MONGODB_TEST_URI=mongodb://localhost:27017/mobile_spo_health_test

# Security
JWT_SECRET=your_super_secure_jwt_secret_here_change_in_production
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Medical AI Configuration
# Using local models for privacy - no external API keys needed
MEDICAL_AI_MODEL_PATH=./models/medical-bert
ENABLE_LOCAL_AI=true
AI_CONFIDENCE_THRESHOLD=0.7
MAX_CONVERSATION_LENGTH=50

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Emergency Services (South Africa)
EMERGENCY_CRISIS_LINE=**********
EMERGENCY_SUICIDE_PREVENTION=080012131
EMERGENCY_SERVICES=10177
EMERGENCY_SMS=31393

# Healthcare Provider Integration
ENABLE_PROVIDER_INTEGRATION=false
PROVIDER_API_URL=https://your-healthcare-provider-api.com

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# CORS
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000

# File Upload
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf

# Health Data Retention (days)
HEALTH_DATA_RETENTION_DAYS=2555
CHAT_HISTORY_RETENTION_DAYS=365
EMERGENCY_LOG_RETENTION_DAYS=3650
