import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  SafeAreaView,
  StatusBar,
  Modal,
  Dimensions,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration - Update this to your computer's IP address for testing on device
const API_BASE_URL = Platform.OS === 'web' ? 'http://localhost:3001' : 'http://********:3001'; // For simulator
// const API_BASE_URL = 'http://YOUR_COMPUTER_IP:3001'; // For physical device

// Types
interface MedicalAIResponse {
  success: boolean;
  data: {
    message: string;
    confidence: number;
    medicalTopics: string[];
    recommendations: string[];
    disclaimers: string[];
    isEmergency: boolean;
    emergencyLevel?: string;
    timestamp: string;
    emergency?: {
      level: string;
      resources: any;
    };
  };
}

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isEmergency?: boolean;
  confidence?: number;
  medicalTopics?: string[];
  recommendations?: string[];
}

interface JournalEntry {
  id: string;
  date: Date;
  mood: number; // 1-5 scale
  symptoms: string[];
  notes: string;
  medications: string[];
}

interface Appointment {
  id: string;
  title: string;
  date: Date;
  time: string;
  type: 'doctor' | 'therapist' | 'specialist' | 'emergency' | 'mobile_clinic' | 'telemedicine';
  provider: string;
  notes: string;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  location?: string;
  queuePosition?: number;
  estimatedWaitTime?: number;
  remindersSent?: boolean;
  checkInTime?: Date;
  priority?: 'normal' | 'urgent' | 'emergency';
  preparationChecklist?: string[];
  followUpRequired?: boolean;
  cost?: number;
  insurance?: string;
}

interface HealthMetric {
  id: string;
  type: 'blood_pressure' | 'heart_rate' | 'weight' | 'temperature' | 'sleep' | 'steps';
  value: string;
  unit: string;
  timestamp: Date;
}

interface CommunityPost {
  id: string;
  author: string;
  content: string;
  timestamp: Date;
  likes: number;
  comments: number;
  category: 'support' | 'question' | 'success' | 'resource';
}

interface Resource {
  id: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'audio' | 'tool' | 'quiz';
  category: 'mental_health' | 'physical_health' | 'nutrition' | 'exercise' | 'emergency';
  url?: string;
  content?: string;
}

interface DidYouKnowFact {
  id: string;
  fact: string;
  category: 'health' | 'nutrition' | 'mental_health' | 'exercise' | 'sleep' | 'wellness';
  source: string;
  funLevel: 1 | 2 | 3 | 4 | 5; // 1 = basic, 5 = mind-blowing
  emoji: string;
  actionTip?: string;
}

interface Quiz {
  id: string;
  title: string;
  description: string;
  category: string;
  questions: QuizQuestion[];
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number; // in minutes
  points: number;
}

interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  funFact?: string;
}

interface UserStreak {
  currentStreak: number;
  longestStreak: number;
  lastCheckIn: Date;
  totalCheckIns: number;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlocked: boolean;
  unlockedDate?: Date;
  progress: number;
  maxProgress: number;
}

const App = () => {
  // Navigation State
  const [currentScreen, setCurrentScreen] = useState<'splash' | 'welcome' | 'home' | 'chat' | 'journal' | 'appointments' | 'community' | 'resources' | 'quiz' | 'profile' | 'emergency'>('splash');
  const [language, setLanguage] = useState('English');
  const [mode, setMode] = useState('Youth');

  // Chat State
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m your AI health assistant. How can I help you today? I can help with health questions, track symptoms, provide mental health support, and connect you with emergency resources if needed.',
      sender: 'ai',
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = React.useRef<string>('');
  const textInputRef = React.useRef<TextInput>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationHistory, setConversationHistory] = useState<Message[]>([]);
  const [chatSessions, setChatSessions] = useState<{[key: string]: Message[]}>({});
  const [currentSessionId, setCurrentSessionId] = useState('default');
  const [lastMessageTime, setLastMessageTime] = useState<Date | null>(null);
  const [messageDeliveryStatus, setMessageDeliveryStatus] = useState<{[key: string]: 'sending' | 'delivered' | 'failed'}>({});

  // Journal State with Sample Entries
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([
    {
      id: '1',
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // Yesterday
      mood: 4,
      symptoms: ['Headache', 'Fatigue'],
      notes: 'Had a good day overall! Felt energetic in the morning after my workout. The headache came in the afternoon - probably from staring at the computer too long. Need to take more breaks.',
      medications: ['Paracetamol 500mg']
    },
    {
      id: '2',
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      mood: 3,
      symptoms: ['Anxiety', 'Stress'],
      notes: 'Work was quite stressful today. Had a big presentation and felt anxious beforehand. Used the breathing exercises from the app which helped a lot. Feeling better now.',
      medications: []
    },
    {
      id: '3',
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      mood: 5,
      symptoms: [],
      notes: 'Amazing day! Went for a hike with friends and felt so refreshed. The fresh air and exercise really boosted my mood. Slept really well too.',
      medications: []
    },
    {
      id: '4',
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      mood: 2,
      symptoms: ['Depression', 'Fatigue', 'Insomnia'],
      notes: 'Struggling a bit today. Didn\'t sleep well last night and feeling quite low. Reached out to my therapist and scheduled an appointment. Taking it one day at a time.',
      medications: []
    },
    {
      id: '5',
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
      mood: 4,
      symptoms: ['Sore throat', 'Cough'],
      notes: 'Coming down with a cold but spirits are good. Drinking lots of tea and honey. Working from home today to rest and not spread anything to colleagues.',
      medications: ['Throat lozenges', 'Vitamin C']
    }
  ]);
  const [currentMood, setCurrentMood] = useState(3);
  const [showQuickHealthCheck, setShowQuickHealthCheck] = useState(true);
  const [currentSymptoms, setCurrentSymptoms] = useState<string[]>([]);
  const [journalNotes, setJournalNotes] = useState('');

  // Health Metrics state
  const [waterIntake, setWaterIntake] = useState(0);
  const [sleepHours, setSleepHours] = useState(0);
  const [exerciseMinutes, setExerciseMinutes] = useState(0);

  // Community state
  const [selectedCommunityTab, setSelectedCommunityTab] = useState('All');

  // Profile state
  const [showProfile, setShowProfile] = useState(false);

  // Streak tracking
  const [currentStreak, setCurrentStreak] = useState(7); // Default 7-day streak

  // Appointments State
  const [appointments, setAppointments] = useState<Appointment[]>([
    {
      id: '1',
      title: 'General Health Checkup',
      date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
      time: '10:00 AM - 10:30 AM',
      type: 'doctor',
      provider: 'Dr. Sarah Johnson',
      notes: 'Annual checkup and blood pressure monitoring',
      status: 'confirmed',
      location: 'Sandton Medical Centre, Room 204',
      queuePosition: 3,
      estimatedWaitTime: 15,
      remindersSent: false,
      priority: 'normal',
      preparationChecklist: ['Bring ID document', 'Bring medical aid card', 'Fast for 12 hours before appointment'],
      followUpRequired: false,
      cost: 450,
      insurance: 'Discovery Health'
    },
    {
      id: '2',
      title: 'Mental Health Consultation',
      date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      time: '2:00 PM - 3:00 PM',
      type: 'therapist',
      provider: 'Dr. Amara Thompson',
      notes: 'Follow-up session for anxiety management',
      status: 'scheduled',
      location: 'Rosebank Psychology Centre',
      priority: 'normal',
      preparationChecklist: ['Complete mood tracking journal', 'Prepare questions about coping strategies'],
      followUpRequired: true,
      cost: 800,
      insurance: 'Momentum Health'
    },
    {
      id: '3',
      title: 'Mobile Clinic Visit',
      date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
      time: '9:00 AM - 12:00 PM',
      type: 'mobile_clinic',
      provider: 'Mobile Health Team',
      notes: 'Community health screening and vaccinations',
      status: 'scheduled',
      location: 'Soweto Community Centre',
      priority: 'normal',
      preparationChecklist: ['Bring vaccination card', 'Wear comfortable clothing'],
      followUpRequired: false,
      cost: 0,
      insurance: 'Public Health'
    }
  ]);
  const [showAppointmentModal, setShowAppointmentModal] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [appointmentFilter, setAppointmentFilter] = useState<'all' | 'upcoming' | 'past' | 'cancelled'>('upcoming');
  const [showAppointmentDetails, setShowAppointmentDetails] = useState(false);
  const [showRescheduleModal, setShowRescheduleModal] = useState(false);
  const [newAppointmentData, setNewAppointmentData] = useState({
    title: '',
    type: 'doctor',
    date: '',
    time: '',
    provider: '',
    location: '',
    notes: ''
  });
  const [availableProviders] = useState([
    { id: '1', name: 'Dr. Sarah Johnson', specialty: 'General Medicine', rating: 4.8, location: 'Cape Town Medical Centre' },
    { id: '2', name: 'Dr. Michael Chen', specialty: 'Mental Health', rating: 4.9, location: 'Wellness Psychology Centre' },
    { id: '3', name: 'Dr. Priya Patel', specialty: 'Cardiology', rating: 4.7, location: 'Heart Health Clinic' },
    { id: '4', name: 'Dr. Emily Carter', specialty: 'Family Medicine', rating: 4.8, location: 'Family Health Clinic' },
    { id: '5', name: 'Dr. James Wilson', specialty: 'Emergency Medicine', rating: 4.6, location: 'Emergency Medical Centre' }
  ]);
  const [availableTimeSlots] = useState([
    '08:00 AM', '08:30 AM', '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM',
    '11:00 AM', '11:30 AM', '02:00 PM', '02:30 PM', '03:00 PM', '03:30 PM',
    '04:00 PM', '04:30 PM', '05:00 PM'
  ]);

  // Health Metrics State
  const [healthMetrics, setHealthMetrics] = useState<HealthMetric[]>([]);

  // Enhanced Community State
  const [communityPosts, setCommunityPosts] = useState<CommunityPost[]>([
    {
      id: '1',
      author: 'Sarah M.',
      avatar: '🌸',
      content: 'Just wanted to share that meditation has really helped with my anxiety. Started with just 5 minutes a day! The Headspace app has been amazing. Anyone else tried mindfulness?',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      likes: 24,
      comments: 8,
      category: 'success',
      tags: ['anxiety', 'meditation', 'mindfulness'],
      supportCount: 15,
      isVerified: true,
      mood: 'positive'
    },
    {
      id: '2',
      author: 'Mike K.',
      avatar: '🌟',
      content: 'Does anyone have tips for managing stress at work? Feeling overwhelmed lately. My manager is putting a lot of pressure and I\'m struggling to cope.',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      likes: 18,
      comments: 12,
      category: 'question',
      tags: ['stress', 'work', 'coping'],
      supportCount: 22,
      isVerified: false,
      mood: 'seeking-help'
    },
    {
      id: '3',
      author: 'Dr. Amara T.',
      avatar: '👩‍⚕️',
      content: 'Remember: It\'s okay to not be okay. Mental health is just as important as physical health. If you\'re struggling, please reach out. You\'re not alone. 💚',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      likes: 45,
      comments: 6,
      category: 'support',
      tags: ['mental-health', 'support', 'professional'],
      supportCount: 38,
      isVerified: true,
      isProfessional: true,
      mood: 'supportive'
    },
    {
      id: '4',
      author: 'Thabo M.',
      avatar: '🌍',
      content: 'Started a walking group in Johannesburg for people dealing with depression. We meet every Saturday at 8am in Delta Park. Exercise + community = healing! 🚶‍♂️🚶‍♀️',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
      likes: 32,
      comments: 15,
      category: 'community-event',
      tags: ['depression', 'exercise', 'johannesburg', 'group'],
      supportCount: 28,
      isVerified: true,
      location: 'Johannesburg',
      mood: 'encouraging'
    },
    {
      id: '5',
      author: 'Anonymous User',
      avatar: '🤗',
      content: 'I\'ve been clean from self-harm for 6 months today. This community has been my lifeline. Thank you all for being here when I needed you most. 💙',
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
      likes: 67,
      comments: 23,
      category: 'milestone',
      tags: ['recovery', 'self-harm', 'milestone', 'gratitude'],
      supportCount: 89,
      isVerified: false,
      isAnonymous: true,
      mood: 'grateful'
    },
    {
      id: '6',
      author: 'Nomsa P.',
      avatar: '🌺',
      content: 'Hosting a virtual support group for new mothers dealing with postpartum depression. Tuesdays 7pm. Safe space, no judgment. DM me for details. 👶💕',
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      likes: 28,
      comments: 9,
      category: 'support-group',
      tags: ['postpartum', 'mothers', 'support-group', 'virtual'],
      supportCount: 34,
      isVerified: true,
      mood: 'nurturing'
    }
  ]);

  const [communityGroups, setCommunityGroups] = useState([
    {
      id: '1',
      name: 'Anxiety Support Circle',
      description: 'A safe space for those dealing with anxiety disorders',
      members: 234,
      category: 'Mental Health',
      isPrivate: true,
      moderator: 'Dr. Sarah L.',
      meetingTime: 'Wednesdays 6PM',
      tags: ['anxiety', 'support', 'weekly']
    },
    {
      id: '2',
      name: 'Depression Warriors',
      description: 'Fighting depression together, one day at a time',
      members: 189,
      category: 'Mental Health',
      isPrivate: true,
      moderator: 'Mike R.',
      meetingTime: 'Sundays 4PM',
      tags: ['depression', 'peer-support', 'weekly']
    },
    {
      id: '3',
      name: 'Mindful Living SA',
      description: 'Mindfulness and meditation practices for South Africans',
      members: 156,
      category: 'Wellness',
      isPrivate: false,
      moderator: 'Thandi M.',
      meetingTime: 'Daily 7AM',
      tags: ['mindfulness', 'meditation', 'daily']
    }
  ]);

  const [peerMentors, setPeerMentors] = useState([
    {
      id: '1',
      name: 'Sarah M.',
      avatar: '🌸',
      specialties: ['Anxiety', 'Meditation', 'Work Stress'],
      experience: '3 years in recovery',
      rating: 4.9,
      isOnline: true,
      responseTime: '< 2 hours',
      languages: ['English', 'Afrikaans']
    },
    {
      id: '2',
      name: 'Dr. Amara T.',
      avatar: '👩‍⚕️',
      specialties: ['Depression', 'Trauma', 'Crisis Support'],
      experience: 'Licensed Psychologist',
      rating: 5.0,
      isOnline: true,
      responseTime: '< 1 hour',
      languages: ['English', 'isiZulu', 'Sesotho'],
      isProfessional: true
    },
    {
      id: '3',
      name: 'Thabo M.',
      avatar: '🌍',
      specialties: ['Addiction Recovery', 'Men\'s Health', 'Exercise Therapy'],
      experience: '5 years sober',
      rating: 4.8,
      isOnline: false,
      responseTime: '< 4 hours',
      languages: ['English', 'isiZulu']
    }
  ]);

  // Enhanced Resources State with Comprehensive Content
  const [resources, setResources] = useState<Resource[]>([
    {
      id: '1',
      title: 'Understanding Anxiety',
      description: 'Learn about anxiety symptoms and coping strategies',
      type: 'article',
      category: 'mental_health',
      content: 'Anxiety is a normal response to stress, but when it becomes overwhelming, it can impact your daily life. Common symptoms include racing heart, sweating, difficulty concentrating, and persistent worry. Effective coping strategies include deep breathing, progressive muscle relaxation, regular exercise, and mindfulness meditation. Remember, seeking professional help is a sign of strength, not weakness.'
    },
    {
      id: '2',
      title: 'Emergency Contacts South Africa',
      description: 'Important crisis and emergency contact numbers',
      type: 'tool',
      category: 'emergency',
      content: '🇿🇦 SOUTH AFRICAN EMERGENCY CONTACTS:\n\n🚨 Emergency Services: 10177\n💙 Crisis Helpline: **********\n🆘 Suicide Prevention: **********\n📱 SADAG SMS: 31393\n🏥 Poison Information: **********\n👮‍♂️ Police: 10111\n🚑 Ambulance: 10177\n🔥 Fire Department: 10177\n\n💚 Remember: You are never alone. Help is always available.'
    },
    {
      id: '3',
      title: 'Mental Health Quiz',
      description: 'Test your knowledge about mental wellness',
      type: 'quiz',
      category: 'mental_health',
      content: 'Interactive quiz about mental health awareness'
    },
    {
      id: '4',
      title: 'Nutrition IQ Challenge',
      description: 'How much do you know about healthy eating?',
      type: 'quiz',
      category: 'nutrition',
      content: 'Fun quiz about nutrition and healthy eating habits'
    },
    {
      id: '5',
      title: 'Sleep Hygiene Guide',
      description: 'Tips for better sleep and rest',
      type: 'article',
      category: 'physical_health',
      content: 'Good sleep is essential for physical and mental health. Create a consistent bedtime routine, keep your bedroom cool and dark, avoid screens 1 hour before bed, and limit caffeine after 2 PM. Adults need 7-9 hours of sleep per night for optimal health.'
    },
    {
      id: '6',
      title: 'Stress Management Toolkit',
      description: 'Practical techniques for managing daily stress',
      type: 'tool',
      category: 'mental_health',
      content: '🧘‍♀️ QUICK STRESS RELIEF:\n• 4-7-8 Breathing: Inhale 4, hold 7, exhale 8\n• Progressive muscle relaxation\n• 5-minute meditation\n• Take a walk outside\n• Listen to calming music\n\n💪 LONG-TERM STRATEGIES:\n• Regular exercise\n• Healthy boundaries\n• Time management\n• Social support\n• Professional counseling'
    },
    {
      id: '7',
      title: 'Heart Health Quiz',
      description: 'Test your cardiovascular health knowledge',
      type: 'quiz',
      category: 'physical_health',
      content: 'Learn about heart health, risk factors, and prevention strategies'
    },
    {
      id: '8',
      title: 'Mindfulness for Beginners',
      description: 'Introduction to mindfulness and meditation',
      type: 'article',
      category: 'mental_health',
      content: 'Mindfulness is the practice of being present in the moment without judgment. Start with just 5 minutes daily: sit comfortably, focus on your breath, and gently return attention to breathing when your mind wanders. Regular practice can reduce stress, improve focus, and enhance emotional well-being.'
    }
  ]);

  // Did You Know State
  const [dailyFact, setDailyFact] = useState<DidYouKnowFact | null>(null);
  const [allFacts, setAllFacts] = useState<DidYouKnowFact[]>([
    {
      id: '1',
      fact: 'Your brain uses 20% of your total energy, even though it only weighs 2% of your body weight!',
      category: 'health',
      source: 'Harvard Medical School',
      funLevel: 4,
      emoji: '🧠',
      actionTip: 'Feed your brain with omega-3 rich foods like fish and nuts!'
    },
    {
      id: '2',
      fact: 'Laughing for 15 minutes burns the same calories as walking for 30 minutes!',
      category: 'wellness',
      source: 'Vanderbilt University',
      funLevel: 5,
      emoji: '😂',
      actionTip: 'Watch a funny video or share jokes with friends today!'
    },
    {
      id: '3',
      fact: 'Your heart beats about 100,000 times per day - that\'s 35 million times per year!',
      category: 'health',
      source: 'American Heart Association',
      funLevel: 3,
      emoji: '❤️',
      actionTip: 'Take care of your heart with 30 minutes of exercise today!'
    },
    {
      id: '4',
      fact: 'Drinking water can boost your metabolism by up to 30% for about an hour!',
      category: 'nutrition',
      source: 'Journal of Clinical Endocrinology',
      funLevel: 4,
      emoji: '💧',
      actionTip: 'Drink a glass of water right now to kickstart your metabolism!'
    },
    {
      id: '5',
      fact: 'Forest bathing (spending time in nature) can reduce stress hormones by 50%!',
      category: 'mental_health',
      source: 'Environmental Health and Preventive Medicine',
      funLevel: 5,
      emoji: '🌲',
      actionTip: 'Spend 20 minutes outside today, even if it\'s just in your garden!'
    }
  ]);

  // Gamification State
  const [userStreak, setUserStreak] = useState<UserStreak>({
    currentStreak: 3,
    longestStreak: 7,
    lastCheckIn: new Date(),
    totalCheckIns: 15
  });

  const [achievements, setAchievements] = useState<Achievement[]>([
    {
      id: '1',
      title: 'Health Explorer',
      description: 'Complete your first health journal entry',
      icon: '🏆',
      unlocked: true,
      unlockedDate: new Date(),
      progress: 1,
      maxProgress: 1
    },
    {
      id: '2',
      title: 'Streak Master',
      description: 'Maintain a 7-day check-in streak',
      icon: '🔥',
      unlocked: false,
      progress: 3,
      maxProgress: 7
    },
    {
      id: '3',
      title: 'Quiz Champion',
      description: 'Complete 5 health quizzes',
      icon: '🎯',
      unlocked: false,
      progress: 0,
      maxProgress: 5
    }
  ]);

  const [userPoints, setUserPoints] = useState(150);
  const [showDidYouKnowModal, setShowDidYouKnowModal] = useState(false);

  // Comprehensive Quiz Data
  const [healthQuizzes, setHealthQuizzes] = useState<Quiz[]>([
    {
      id: 'mental_health_quiz',
      title: 'Mental Health Awareness Quiz',
      description: 'Test your knowledge about mental health and wellness',
      category: 'Mental Health',
      difficulty: 'easy',
      estimatedTime: 5,
      points: 50,
      questions: [
        {
          id: 'mh1',
          question: 'What percentage of people will experience a mental health condition in their lifetime?',
          options: ['1 in 10', '1 in 5', '1 in 4', '1 in 2'],
          correctAnswer: 2,
          explanation: '1 in 4 people will experience a mental health condition at some point in their lives.',
          funFact: 'Mental health conditions are more common than you might think - you\'re definitely not alone!'
        },
        {
          id: 'mh2',
          question: 'Which of these is NOT a healthy way to cope with stress?',
          options: ['Exercise', 'Talking to friends', 'Avoiding all social situations', 'Deep breathing'],
          correctAnswer: 2,
          explanation: 'While some alone time is healthy, completely avoiding social situations can worsen stress and anxiety.',
          funFact: 'Social connections are one of the strongest predictors of mental well-being!'
        },
        {
          id: 'mh3',
          question: 'How much sleep do adults need for optimal mental health?',
          options: ['5-6 hours', '7-9 hours', '10-12 hours', 'It doesn\'t matter'],
          correctAnswer: 1,
          explanation: 'Adults need 7-9 hours of sleep for optimal physical and mental health.',
          funFact: 'Your brain literally cleans itself while you sleep, removing toxins that build up during the day!'
        }
      ]
    },
    {
      id: 'nutrition_quiz',
      title: 'Nutrition IQ Challenge',
      description: 'How much do you know about healthy eating?',
      category: 'Nutrition',
      difficulty: 'medium',
      estimatedTime: 7,
      points: 75,
      questions: [
        {
          id: 'n1',
          question: 'How many servings of fruits and vegetables should you eat daily?',
          options: ['3-4 servings', '5-7 servings', '8-10 servings', '1-2 servings'],
          correctAnswer: 1,
          explanation: 'The recommended intake is 5-7 servings of fruits and vegetables daily for optimal health.',
          funFact: 'Eating a rainbow of colors ensures you get a variety of nutrients and antioxidants!'
        },
        {
          id: 'n2',
          question: 'Which nutrient is most important for brain health?',
          options: ['Vitamin C', 'Omega-3 fatty acids', 'Iron', 'Calcium'],
          correctAnswer: 1,
          explanation: 'Omega-3 fatty acids are crucial for brain health, memory, and cognitive function.',
          funFact: 'Your brain is about 60% fat, so healthy fats are essential for optimal brain function!'
        },
        {
          id: 'n3',
          question: 'How much water should the average adult drink daily?',
          options: ['4 glasses', '6 glasses', '8 glasses', '12 glasses'],
          correctAnswer: 2,
          explanation: 'The general recommendation is about 8 glasses (2 liters) of water daily.',
          funFact: 'Even mild dehydration can affect your mood, energy levels, and cognitive performance!'
        }
      ]
    },
    {
      id: 'heart_health_quiz',
      title: 'Heart Health Quiz',
      description: 'Test your cardiovascular health knowledge',
      category: 'Physical Health',
      difficulty: 'medium',
      estimatedTime: 6,
      points: 60,
      questions: [
        {
          id: 'h1',
          question: 'What is considered a normal resting heart rate for adults?',
          options: ['40-60 bpm', '60-100 bpm', '100-120 bpm', '120-140 bpm'],
          correctAnswer: 1,
          explanation: 'A normal resting heart rate for adults is typically 60-100 beats per minute.',
          funFact: 'Athletes often have resting heart rates as low as 40-60 bpm due to their excellent cardiovascular fitness!'
        },
        {
          id: 'h2',
          question: 'Which exercise is best for heart health?',
          options: ['Weight lifting only', 'Aerobic exercise', 'Yoga only', 'Stretching only'],
          correctAnswer: 1,
          explanation: 'Aerobic exercise like walking, swimming, or cycling is best for cardiovascular health.',
          funFact: 'Just 30 minutes of moderate exercise 5 days a week can reduce heart disease risk by 50%!'
        },
        {
          id: 'h3',
          question: 'What blood pressure reading is considered high?',
          options: ['120/80 or higher', '130/80 or higher', '140/90 or higher', '150/100 or higher'],
          correctAnswer: 2,
          explanation: 'Blood pressure of 140/90 mmHg or higher is generally considered high blood pressure.',
          funFact: 'High blood pressure is called the "silent killer" because it often has no symptoms!'
        }
      ]
    }
  ]);

  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [quizScore, setQuizScore] = useState(0);
  const [showQuizResult, setShowQuizResult] = useState(false);
  const [quizAnswers, setQuizAnswers] = useState<number[]>([]);

  // Modal States
  const [showEmergencyModal, setShowEmergencyModal] = useState(false);
  const [showJournalModal, setShowJournalModal] = useState(false);
  const [showNewJournalEntry, setShowNewJournalEntry] = useState(false);
  const [editingJournalEntry, setEditingJournalEntry] = useState<JournalEntry | null>(null);
  const [newJournalData, setNewJournalData] = useState({
    mood: 3,
    symptoms: [] as string[],
    notes: '',
    medications: '',
    sleepHours: '',
    waterIntake: '',
    exerciseMinutes: '',
    stressLevel: 3,
    energyLevel: 3
  });

  // Appointment Booking State
  const [bookingStep, setBookingStep] = useState<'type' | 'provider' | 'datetime' | 'details' | 'confirmation'>('type');
  const [selectedAppointmentType, setSelectedAppointmentType] = useState<string>('');
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>('');
  const [appointmentNotes, setAppointmentNotes] = useState<string>('');
  const [urgencyLevel, setUrgencyLevel] = useState<'normal' | 'urgent' | 'emergency'>('normal');

  // Common symptoms for quick selection
  const commonSymptoms = [
    'Headache', 'Fatigue', 'Nausea', 'Fever', 'Cough', 'Sore throat',
    'Anxiety', 'Depression', 'Stress', 'Insomnia', 'Back pain', 'Joint pain'
  ];

  // Appointment booking data
  const appointmentTypes = [
    { id: 'doctor', name: 'General Doctor', icon: '👨‍⚕️', description: 'General health checkup and consultation', duration: 30 },
    { id: 'therapist', name: 'Mental Health Therapist', icon: '🧠', description: 'Mental health counseling and therapy', duration: 60 },
    { id: 'specialist', name: 'Specialist Consultation', icon: '🔬', description: 'Specialized medical consultation', duration: 45 },
    { id: 'mobile_clinic', name: 'Mobile Clinic', icon: '🚐', description: 'Community mobile health services', duration: 20 },
    { id: 'telemedicine', name: 'Telemedicine', icon: '💻', description: 'Virtual consultation from home', duration: 30 },
    { id: 'emergency', name: 'Emergency Consultation', icon: '🚨', description: 'Urgent medical attention needed', duration: 15 }
  ];

  const availableProvidersByType = {
    doctor: [
      { id: 'dr_johnson', name: 'Dr. Sarah Johnson', specialty: 'General Medicine', rating: 4.8, location: 'Sandton Medical Centre' },
      { id: 'dr_patel', name: 'Dr. Raj Patel', specialty: 'Family Medicine', rating: 4.9, location: 'Rosebank Clinic' },
      { id: 'dr_williams', name: 'Dr. Lisa Williams', specialty: 'Internal Medicine', rating: 4.7, location: 'Johannesburg Hospital' }
    ],
    therapist: [
      { id: 'dr_thompson', name: 'Dr. Amara Thompson', specialty: 'Clinical Psychology', rating: 5.0, location: 'Rosebank Psychology Centre' },
      { id: 'dr_mthembu', name: 'Dr. Nomsa Mthembu', specialty: 'Trauma Therapy', rating: 4.9, location: 'Soweto Mental Health Clinic' },
      { id: 'dr_van_der_merwe', name: 'Dr. Pieter van der Merwe', specialty: 'Anxiety & Depression', rating: 4.8, location: 'Cape Town Psychology Practice' }
    ],
    specialist: [
      { id: 'dr_cardiologist', name: 'Dr. Michael Chen', specialty: 'Cardiology', rating: 4.9, location: 'Heart & Vascular Centre' },
      { id: 'dr_dermatologist', name: 'Dr. Fatima Al-Rashid', specialty: 'Dermatology', rating: 4.8, location: 'Skin Health Clinic' }
    ],
    mobile_clinic: [
      { id: 'mobile_team_1', name: 'Mobile Health Team A', specialty: 'Community Health', rating: 4.7, location: 'Various Locations' }
    ],
    telemedicine: [
      { id: 'tele_dr_1', name: 'Dr. Virtual Care', specialty: 'Telemedicine', rating: 4.6, location: 'Online' }
    ],
    emergency: [
      { id: 'emergency_team', name: 'Emergency Medical Team', specialty: 'Emergency Medicine', rating: 4.9, location: 'Emergency Department' }
    ]
  };

  const timeSlots = [
    '08:00 AM', '08:30 AM', '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM',
    '11:00 AM', '11:30 AM', '12:00 PM', '12:30 PM', '01:00 PM', '01:30 PM',
    '02:00 PM', '02:30 PM', '03:00 PM', '03:30 PM', '04:00 PM', '04:30 PM'
  ];

  // Mood labels
  const moodLabels = ['Very Bad', 'Bad', 'Okay', 'Good', 'Excellent'];
  const moodEmojis = ['😢', '😕', '😐', '😊', '😄'];

  // Utility Functions
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-ZA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Enhanced Appointment Management Functions
  const confirmAppointment = (appointmentId: string) => {
    setAppointments(prev => prev.map(apt =>
      apt.id === appointmentId
        ? { ...apt, status: 'confirmed' as any }
        : apt
    ));
    Alert.alert('Appointment Confirmed! ✅', 'Your appointment has been confirmed successfully. You will receive reminders before your appointment.');
  };

  const cancelAppointment = (appointmentId: string) => {
    const appointment = appointments.find(apt => apt.id === appointmentId);
    Alert.alert(
      'Cancel Appointment',
      `Are you sure you want to cancel your appointment with ${appointment?.provider}?`,
      [
        { text: 'Keep Appointment', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => {
            setAppointments(prev => prev.map(apt =>
              apt.id === appointmentId
                ? { ...apt, status: 'cancelled' as any }
                : apt
            ));
            Alert.alert('Appointment Cancelled', 'Your appointment has been cancelled. You can book a new one anytime.');
          }
        }
      ]
    );
  };

  const checkInAppointment = (appointmentId: string) => {
    setAppointments(prev => prev.map(apt =>
      apt.id === appointmentId
        ? { ...apt, status: 'in_progress' as any, queuePosition: 1, estimatedWaitTime: 5 }
        : apt
    ));
    Alert.alert('Checked In Successfully! 🏥', 'You have been checked in. Please wait to be called by the healthcare provider.');
  };

  const rescheduleAppointment = (appointmentId: string, newDate: string, newTime: string) => {
    setAppointments(prev => prev.map(apt =>
      apt.id === appointmentId
        ? {
            ...apt,
            date: new Date(newDate),
            time: newTime,
            status: 'rescheduled' as any,
            queuePosition: Math.floor(Math.random() * 5) + 1,
            estimatedWaitTime: Math.floor(Math.random() * 30) + 15
          }
        : apt
    ));
    setShowRescheduleModal(false);
    Alert.alert('Appointment Rescheduled! 📅', 'Your appointment has been rescheduled successfully. New confirmation details have been sent.');
  };

  // Helper functions for appointment status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#10B981';
      case 'scheduled': return '#F59E0B';
      case 'in_progress': return '#3B82F6';
      case 'completed': return '#6B7280';
      case 'cancelled': return '#EF4444';
      case 'rescheduled': return '#8B5CF6';
      default: return '#6B7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return '✅';
      case 'scheduled': return '📅';
      case 'in_progress': return '🏥';
      case 'completed': return '✓';
      case 'cancelled': return '❌';
      case 'rescheduled': return '📝';
      default: return '📋';
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  // Enhanced Journal Functions
  const addJournalEntry = () => {
    const newEntry: JournalEntry = {
      id: Date.now().toString(),
      date: new Date(),
      mood: newJournalData.mood,
      symptoms: newJournalData.symptoms,
      notes: newJournalData.notes,
      medications: newJournalData.medications ? [newJournalData.medications] : [],
      sleepHours: parseInt(newJournalData.sleepHours) || 0,
      waterIntake: parseInt(newJournalData.waterIntake) || 0,
      exerciseMinutes: parseInt(newJournalData.exerciseMinutes) || 0,
      stressLevel: newJournalData.stressLevel,
      energyLevel: newJournalData.energyLevel,
      tags: []
    };

    setJournalEntries(prev => [newEntry, ...prev]);

    // Reset form
    setNewJournalData({
      mood: 3,
      symptoms: [],
      notes: '',
      medications: '',
      sleepHours: '',
      waterIntake: '',
      exerciseMinutes: '',
      stressLevel: 3,
      energyLevel: 3
    });

    setShowNewJournalEntry(false);
    Alert.alert('Entry Added! 📝', 'Your journal entry has been saved successfully. Keep tracking your health journey!');

    // Add points for journaling
    addPoints(10);

    // Check for achievements
    if (journalEntries.length + 1 >= 7) {
      setAchievements(prev => prev.map(achievement => {
        if (achievement.id === '3' && !achievement.unlocked) {
          addPoints(50);
          Alert.alert('Achievement Unlocked! 🏆', 'Health Journal Master - You\'ve logged 7 journal entries!');
          return { ...achievement, unlocked: true, unlockedDate: new Date() };
        }
        return achievement;
      }));
    }
  };

  const updateJournalEntry = () => {
    if (!editingJournalEntry) return;

    const updatedEntry: JournalEntry = {
      ...editingJournalEntry,
      mood: newJournalData.mood,
      symptoms: newJournalData.symptoms,
      notes: newJournalData.notes,
      medications: newJournalData.medications ? [newJournalData.medications] : [],
      sleepHours: parseInt(newJournalData.sleepHours) || 0,
      waterIntake: parseInt(newJournalData.waterIntake) || 0,
      exerciseMinutes: parseInt(newJournalData.exerciseMinutes) || 0,
      stressLevel: newJournalData.stressLevel,
      energyLevel: newJournalData.energyLevel
    };

    setJournalEntries(prev => prev.map(entry =>
      entry.id === editingJournalEntry.id ? updatedEntry : entry
    ));

    setNewJournalData({
      mood: 3,
      symptoms: [],
      notes: '',
      medications: '',
      sleepHours: '',
      waterIntake: '',
      exerciseMinutes: '',
      stressLevel: 3,
      energyLevel: 3
    });
    setEditingJournalEntry(null);
    setShowNewJournalEntry(false);

    Alert.alert('Entry Updated! ✅', 'Your journal entry has been updated successfully.');
  };

  // Stable journal input handlers to prevent keyboard dismissal
  const handleJournalInputChange = React.useCallback((field: string, value: string | number) => {
    setNewJournalData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const toggleSymptom = React.useCallback((symptom: string) => {
    setNewJournalData(prev => ({
      ...prev,
      symptoms: prev.symptoms.includes(symptom)
        ? prev.symptoms.filter(s => s !== symptom)
        : [...prev.symptoms, symptom]
    }));
  }, []);

  // Health Metrics Functions
  const updateWaterIntake = (change: number) => {
    setWaterIntake(prev => Math.max(0, prev + change));
  };

  const updateSleepHours = (change: number) => {
    setSleepHours(prev => Math.max(0, Math.min(24, prev + change)));
  };

  const updateExerciseMinutes = (change: number) => {
    setExerciseMinutes(prev => Math.max(0, prev + change));
  };

  // Appointment Booking Functions
  const resetBookingForm = () => {
    setBookingStep('type');
    setSelectedAppointmentType('');
    setSelectedProvider('');
    setSelectedDate(new Date());
    setSelectedTimeSlot('');
    setAppointmentNotes('');
    setUrgencyLevel('normal');
  };

  const bookAppointment = () => {
    const selectedTypeData = appointmentTypes.find(type => type.id === selectedAppointmentType);
    const selectedProviderData = availableProvidersByType[selectedAppointmentType as keyof typeof availableProvidersByType]?.find(
      provider => provider.id === selectedProvider
    );

    if (!selectedTypeData || !selectedProviderData) {
      Alert.alert('Error', 'Please complete all booking steps');
      return;
    }

    const newAppointment: Appointment = {
      id: Date.now().toString(),
      title: selectedTypeData.name,
      date: selectedDate,
      time: selectedTimeSlot,
      type: selectedAppointmentType as any,
      provider: selectedProviderData.name,
      notes: appointmentNotes,
      status: urgencyLevel === 'emergency' ? 'confirmed' : 'scheduled',
      location: selectedProviderData.location,
      priority: urgencyLevel,
      preparationChecklist: getPreparationChecklist(selectedAppointmentType),
      followUpRequired: selectedAppointmentType === 'therapist',
      cost: getAppointmentCost(selectedAppointmentType),
      insurance: 'Discovery Health'
    };

    setAppointments(prev => [...prev, newAppointment]);
    setShowAppointmentModal(false);
    resetBookingForm();

    Alert.alert(
      'Appointment Booked!',
      `Your ${selectedTypeData.name} appointment with ${selectedProviderData.name} has been ${urgencyLevel === 'emergency' ? 'confirmed' : 'scheduled'} for ${formatDate(selectedDate)} at ${selectedTimeSlot}.`,
      [
        { text: 'OK', onPress: () => setCurrentScreen('appointments') }
      ]
    );
  };

  const getPreparationChecklist = (type: string): string[] => {
    switch (type) {
      case 'doctor':
        return ['Bring ID document', 'Bring medical aid card', 'List current medications', 'Prepare questions'];
      case 'therapist':
        return ['Complete mood tracking journal', 'Prepare discussion topics', 'Bring notebook'];
      case 'specialist':
        return ['Bring referral letter', 'Bring previous test results', 'List symptoms timeline'];
      case 'mobile_clinic':
        return ['Bring vaccination card', 'Wear comfortable clothing', 'Arrive early'];
      case 'telemedicine':
        return ['Test internet connection', 'Find quiet space', 'Prepare device camera'];
      case 'emergency':
        return ['Bring ID and medical aid', 'List current symptoms', 'Contact emergency contact'];
      default:
        return ['Bring ID document', 'Arrive on time'];
    }
  };

  const getAppointmentCost = (type: string): number => {
    switch (type) {
      case 'doctor': return 450;
      case 'therapist': return 800;
      case 'specialist': return 650;
      case 'mobile_clinic': return 0;
      case 'telemedicine': return 350;
      case 'emergency': return 1200;
      default: return 400;
    }
  };

  const getAvailableTimeSlots = (date: Date, providerType: string): string[] => {
    // Smart time slot optimization based on provider type and date
    const dayOfWeek = date.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

    if (providerType === 'emergency') {
      return ['Available 24/7'];
    }

    if (providerType === 'mobile_clinic') {
      return isWeekend ? [] : ['09:00 AM - 12:00 PM', '01:00 PM - 04:00 PM'];
    }

    if (isWeekend) {
      return timeSlots.slice(4, 12); // Limited weekend hours
    }

    return timeSlots;
  };

  const checkAppointmentConflicts = (date: Date, time: string): boolean => {
    return appointments.some(apt =>
      apt.date.toDateString() === date.toDateString() &&
      apt.time.includes(time.split(' ')[0]) &&
      apt.status !== 'cancelled'
    );
  };

  // Enhanced Journal Entry Actions
  const shareJournalEntry = (entryId: string) => {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) return;

    Alert.alert(
      'Share Journal Entry',
      `Share your journal entry from ${formatDate(entry.date)} with your healthcare provider or support network?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Share with Doctor',
          onPress: () => Alert.alert('Shared! 📤', 'Your journal entry has been securely shared with your healthcare provider.')
        },
        {
          text: 'Share with Support Network',
          onPress: () => Alert.alert('Shared! 💙', 'Your journal entry has been shared with your trusted support network.')
        }
      ]
    );
  };

  const editJournalEntry = (entryId: string) => {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) return;

    setEditingJournalEntry(entry);
    setNewJournalData({
      mood: entry.mood,
      symptoms: entry.symptoms,
      notes: entry.notes,
      medications: entry.medications.join(', '),
      sleepHours: entry.sleepHours?.toString() || '',
      waterIntake: entry.waterIntake?.toString() || '',
      exerciseMinutes: entry.exerciseMinutes?.toString() || '',
      stressLevel: entry.stressLevel || 3,
      energyLevel: entry.energyLevel || 3
    });
    setShowNewJournalEntry(true);
  };

  const deleteJournalEntry = (entryId: string) => {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) return;

    Alert.alert(
      'Delete Journal Entry',
      `Are you sure you want to delete your journal entry from ${formatDate(entry.date)}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setJournalEntries(prev => prev.filter(entry => entry.id !== entryId));
            Alert.alert('Entry Deleted', 'Your journal entry has been permanently deleted.');
          }
        }
      ]
    );
  };

  const duplicateJournalEntry = (entryId: string) => {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) return;

    const duplicatedEntry: JournalEntry = {
      ...entry,
      id: Date.now().toString(),
      date: new Date(),
      notes: entry.notes + ' (Copy)'
    };

    setJournalEntries(prev => [duplicatedEntry, ...prev]);
    Alert.alert('Entry Duplicated! 📋', 'A copy of your journal entry has been created for today.');
  };

  // Enhanced Community Post Actions
  const likePost = (postId: string) => {
    setCommunityPosts(prev => prev.map(post =>
      post.id === postId
        ? { ...post, likes: post.likes + 1 }
        : post
    ));
    addPoints(2); // Reward engagement

    // Show encouraging feedback
    const encouragingMessages = [
      '💚 Your support means everything!',
      '🌟 Spreading positivity in the community!',
      '🤝 Thank you for being supportive!',
      '✨ Your kindness makes a difference!'
    ];
    const randomMessage = encouragingMessages[Math.floor(Math.random() * encouragingMessages.length)];
    Alert.alert('Support Sent', randomMessage);
  };

  const commentOnPost = (postId: string) => {
    const post = communityPosts.find(p => p.id === postId);
    const supportiveComments = [
      "Thank you for sharing this. You're so brave! 💪",
      "I'm going through something similar. You're not alone! 🤗",
      "This really resonates with me. Sending you love! 💕",
      "Your strength inspires me. Keep going! 🌟",
      "I'm here if you need someone to talk to. 💙"
    ];

    const randomComment = supportiveComments[Math.floor(Math.random() * supportiveComments.length)];

    Alert.alert(
      'Add Comment',
      `Your supportive comment: "${randomComment}"`,
      [
        { text: 'Edit', style: 'default' },
        {
          text: 'Send',
          onPress: () => {
            setCommunityPosts(prev => prev.map(p =>
              p.id === postId
                ? { ...p, comments: p.comments + 1 }
                : p
            ));
            addPoints(3);
            Alert.alert('Comment Posted', '💬 Your supportive comment has been shared!');
          }
        }
      ]
    );
  };

  const supportPost = (postId: string) => {
    const post = communityPosts.find(p => p.id === postId);
    setCommunityPosts(prev => prev.map(p =>
      p.id === postId
        ? { ...p, supportCount: p.supportCount + 1 }
        : p
    ));

    const supportActions = [
      '🤝 Sent virtual hug and encouragement',
      '💚 Offered to be a listening ear',
      '🌟 Shared helpful resources privately',
      '💪 Sent strength and positive energy',
      '🫂 Offered peer support connection'
    ];

    const randomAction = supportActions[Math.floor(Math.random() * supportActions.length)];
    addPoints(5); // Reward supporting others

    Alert.alert(
      'Support Sent Successfully',
      `${randomAction}\n\nYour support helps create a safer, more caring community. Thank you! 💙`,
      [{ text: 'Continue Supporting', style: 'default' }]
    );
  };

  const reportPost = (postId: string) => {
    Alert.alert(
      'Report Content',
      'Help us keep the community safe. What\'s the concern?',
      [
        { text: 'Inappropriate Content', onPress: () => handleReport(postId, 'inappropriate') },
        { text: 'Spam', onPress: () => handleReport(postId, 'spam') },
        { text: 'Crisis/Self-Harm', onPress: () => handleCrisisReport(postId) },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const handleReport = (postId: string, reason: string) => {
    Alert.alert(
      'Report Submitted',
      'Thank you for helping keep our community safe. Our moderation team will review this within 2 hours.',
      [{ text: 'OK' }]
    );
  };

  const handleCrisisReport = (postId: string) => {
    Alert.alert(
      'Crisis Support Activated',
      'Thank you for caring about community safety. We\'ve:\n\n• Notified our crisis response team\n• Sent support resources to the user\n• Connected them with immediate help\n\nYour quick action may have saved a life. 💙',
      [
        { text: 'Learn More About Crisis Support', onPress: () => showCrisisResources() },
        { text: 'OK' }
      ]
    );
  };

  const showCrisisResources = () => {
    Alert.alert(
      '🆘 Crisis Support Resources',
      'If you or someone you know needs immediate help:\n\n🇿🇦 SA Depression & Anxiety Group: **********\n🇿🇦 Lifeline: 0800150150\n🇿🇦 SADAG SMS: 31393\n🚨 Emergency: 10177\n\nOur community has trained peer supporters available 24/7.',
      [{ text: 'Save These Numbers' }]
    );
  };

  const joinSupportGroup = (groupId: string) => {
    const group = communityGroups.find(g => g.id === groupId);
    Alert.alert(
      `Join ${group?.name}`,
      `You're about to join a safe, moderated support group.\n\n👥 ${group?.members} members\n⏰ Meets ${group?.meetingTime}\n👨‍⚕️ Moderated by ${group?.moderator}\n\nAll conversations are confidential and follow our community guidelines.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Join Group',
          onPress: () => {
            Alert.alert(
              'Welcome to the Group!',
              '🎉 You\'ve successfully joined! You\'ll receive a welcome message from the moderator within 24 hours.\n\n📱 Group notifications are now enabled\n🔒 Remember: What\'s shared in group, stays in group',
              [{ text: 'Start Participating' }]
            );
            addPoints(10);
          }
        }
      ]
    );
  };

  const connectWithMentor = (mentorId: string) => {
    const mentor = peerMentors.find(m => m.id === mentorId);
    Alert.alert(
      `Connect with ${mentor?.name}`,
      `${mentor?.isProfessional ? '👩‍⚕️ Licensed Professional' : '🤝 Peer Supporter'}\n\n✨ Specialties: ${mentor?.specialties.join(', ')}\n⭐ Rating: ${mentor?.rating}/5.0\n⚡ Response time: ${mentor?.responseTime}\n🗣️ Languages: ${mentor?.languages.join(', ')}\n\n${mentor?.isOnline ? '🟢 Online now' : '🟡 Will respond soon'}`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Message',
          onPress: () => {
            Alert.alert(
              'Connection Request Sent',
              `📨 Your request has been sent to ${mentor?.name}!\n\n💬 They typically respond within ${mentor?.responseTime}\n🔒 All conversations are private and confidential\n💙 Remember: You\'re taking a brave step by reaching out`,
              [{ text: 'Continue Browsing' }]
            );
            addPoints(8);
          }
        }
      ]
    );
  };

  // Quiz Functions
  const startQuiz = (quiz: Quiz) => {
    setCurrentQuiz(quiz);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setQuizScore(0);
    setShowQuizResult(false);
    setQuizAnswers([]);
    setCurrentScreen('quiz');
  };

  const selectQuizAnswer = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
  };

  const submitQuizAnswer = () => {
    if (selectedAnswer === null || !currentQuiz) return;

    const currentQuestion = currentQuiz.questions[currentQuestionIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;

    if (isCorrect) {
      setQuizScore(prev => prev + 1);
      addPoints(10); // Reward correct answers
    }

    const newAnswers = [...quizAnswers, selectedAnswer];
    setQuizAnswers(newAnswers);

    // Show explanation
    Alert.alert(
      isCorrect ? '✅ Correct!' : '❌ Not quite right',
      `${currentQuestion.explanation}\n\n💡 Fun Fact: ${currentQuestion.funFact}`,
      [
        {
          text: currentQuestionIndex < currentQuiz.questions.length - 1 ? 'Next Question' : 'See Results',
          onPress: () => {
            if (currentQuestionIndex < currentQuiz.questions.length - 1) {
              setCurrentQuestionIndex(prev => prev + 1);
              setSelectedAnswer(null);
            } else {
              finishQuiz();
            }
          }
        }
      ]
    );
  };

  const finishQuiz = () => {
    if (!currentQuiz) return;

    const percentage = Math.round((quizScore / currentQuiz.questions.length) * 100);
    let message = '';
    let bonusPoints = 0;

    if (percentage >= 90) {
      message = '🏆 Outstanding! You\'re a health knowledge champion!';
      bonusPoints = 50;
    } else if (percentage >= 70) {
      message = '🌟 Great job! You have solid health knowledge!';
      bonusPoints = 30;
    } else if (percentage >= 50) {
      message = '👍 Good effort! Keep learning about health!';
      bonusPoints = 20;
    } else {
      message = '💪 Keep learning! Every step towards health knowledge counts!';
      bonusPoints = 10;
    }

    addPoints(bonusPoints);
    setShowQuizResult(true);

    Alert.alert(
      'Quiz Complete! 🎉',
      `${message}\n\nScore: ${quizScore}/${currentQuiz.questions.length} (${percentage}%)\nBonus Points: +${bonusPoints}`,
      [
        { text: 'Review Answers', onPress: () => setShowQuizResult(true) },
        {
          text: 'Take Another Quiz',
          onPress: () => {
            setCurrentQuiz(null);
            setCurrentScreen('resources');
          }
        }
      ]
    );
  };

  const exitQuiz = () => {
    Alert.alert(
      'Exit Quiz?',
      'Are you sure you want to exit? Your progress will be lost.',
      [
        { text: 'Continue Quiz', style: 'cancel' },
        {
          text: 'Exit',
          style: 'destructive',
          onPress: () => {
            setCurrentQuiz(null);
            setCurrentScreen('resources');
          }
        }
      ]
    );
  };

  // Resource Category Actions
  const openResourceCategory = (category: string) => {
    const categoryResources = resources.filter(r => r.category === category);
    if (categoryResources.length > 0) {
      Alert.alert(
        category.replace('_', ' ').toUpperCase(),
        `Found ${categoryResources.length} resources in this category. Opening first resource...`,
        [
          { text: 'Browse All', onPress: () => Alert.alert('Resource Browser', 'This would open a full resource browser') },
          { text: 'Open First', onPress: () => openResource(categoryResources[0]) }
        ]
      );
    } else {
      Alert.alert(category, `Opening ${category} resources and information`);
    }
  };

  const openResource = (resource: Resource) => {
    if (resource.type === 'quiz') {
      const quiz = healthQuizzes.find(q => q.title === resource.title);
      if (quiz) {
        startQuiz(quiz);
      } else {
        Alert.alert('Quiz', `Starting ${resource.title}...`);
      }
    } else {
      Alert.alert(
        resource.title,
        resource.content || resource.description,
        [{ text: 'Close' }]
      );
    }
  };

  // Emergency Contact Actions
  const callEmergencyNumber = (number: string, service: string) => {
    Alert.alert('Emergency Call', `Call ${service} at ${number}?`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Call', onPress: () => Alert.alert('Calling...', `Connecting to ${service}`) }
    ]);
  };

  // Gamification Functions
  const getDailyFact = async () => {
    try {
      const today = new Date().toDateString();
      const savedDate = await AsyncStorage.getItem('lastFactDate');

      if (savedDate !== today) {
        const randomFact = allFacts[Math.floor(Math.random() * allFacts.length)];
        setDailyFact(randomFact);
        await AsyncStorage.setItem('lastFactDate', today);
        await AsyncStorage.setItem('dailyFact', JSON.stringify(randomFact));
      } else {
        const savedFact = await AsyncStorage.getItem('dailyFact');
        if (savedFact) {
          setDailyFact(JSON.parse(savedFact));
        }
      }
    } catch (error) {
      console.log('Error with AsyncStorage:', error);
      // Fallback to random fact
      const randomFact = allFacts[Math.floor(Math.random() * allFacts.length)];
      setDailyFact(randomFact);
    }
  };

  const updateStreak = () => {
    const today = new Date();
    const lastCheckIn = new Date(userStreak.lastCheckIn);
    const daysDiff = Math.floor((today.getTime() - lastCheckIn.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff === 1) {
      // Continue streak
      setUserStreak(prev => ({
        ...prev,
        currentStreak: prev.currentStreak + 1,
        longestStreak: Math.max(prev.longestStreak, prev.currentStreak + 1),
        lastCheckIn: today,
        totalCheckIns: prev.totalCheckIns + 1
      }));
    } else if (daysDiff > 1) {
      // Reset streak
      setUserStreak(prev => ({
        ...prev,
        currentStreak: 1,
        lastCheckIn: today,
        totalCheckIns: prev.totalCheckIns + 1
      }));
    }
  };

  const addPoints = (points: number) => {
    setUserPoints(prev => prev + points);
  };

  const checkAchievements = () => {
    setAchievements(prev => prev.map(achievement => {
      if (achievement.id === '2' && userStreak.currentStreak >= 7 && !achievement.unlocked) {
        return { ...achievement, unlocked: true, unlockedDate: new Date() };
      }
      return achievement;
    }));
  };

  // Initialize daily fact on app load
  useEffect(() => {
    getDailyFact();
    updateStreak();
    checkAchievements();
  }, []);

  useEffect(() => {
    checkAchievements();
  }, [userStreak]);

  // Splash screen timer effect
  useEffect(() => {
    if (currentScreen === 'splash') {
      const timer = setTimeout(() => {
        setCurrentScreen('welcome');
      }, 3000); // Show splash for 3 seconds
      return () => clearTimeout(timer);
    }
  }, [currentScreen]);

  // API Functions
  const sendChatMessage = async (message: string): Promise<MedicalAIResponse> => {
    console.log('Sending message to backend:', message);
    console.log('API URL:', `${API_BASE_URL}/api/v1/test/chat`);

    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/test/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ message }),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('Response data:', data);
      return data;
    } catch (error) {
      console.error('Fetch error:', error);

      // Enhanced Mock AI Response System for Demo
      const generateMockResponse = (userMessage: string): MedicalAIResponse => {
        const lowerMessage = userMessage.toLowerCase();

        // Emergency detection patterns
        const emergencyKeywords = [
          'suicide', 'kill myself', 'end my life', 'want to die', 'hurt myself',
          'self harm', 'cutting', 'overdose', 'emergency', 'urgent', 'crisis'
        ];

        const isEmergency = emergencyKeywords.some(keyword => lowerMessage.includes(keyword));

        if (isEmergency) {
          return {
            success: true,
            data: {
              message: "🚨 I'm very concerned about what you've shared. Your safety is the most important thing right now.\n\n💙 **You are not alone** - there are people who want to help you.\n\n🆘 **Please reach out immediately**:\n• Crisis Helpline: **********\n• Suicide Prevention: **********\n• Emergency Services: 10177\n• SADAG SMS: 31393\n\n🤝 Consider reaching out to a trusted friend, family member, or going to your nearest emergency room.\n\n💚 Your life has value and help is available 24/7.",
              confidence: 0.95,
              medicalTopics: ['crisis_intervention', 'emergency_support'],
              recommendations: [
                'Contact emergency services immediately if in immediate danger',
                'Reach out to a trusted friend, family member, or healthcare provider',
                'Call a crisis helpline for immediate support',
                'Go to your nearest emergency room if you feel unsafe'
              ],
              disclaimers: ['This is an emergency situation - please seek immediate professional help'],
              isEmergency: true,
              emergencyLevel: 'critical',
              timestamp: new Date().toISOString(),
              emergency: {
                level: 'critical',
                resources: [
                  { name: 'Crisis Helpline', phone: '**********', description: '24/7 Crisis support' },
                  { name: 'Suicide Prevention', phone: '**********', description: 'Suicide prevention hotline' },
                  { name: 'Emergency Services', phone: '10177', description: 'Emergency medical services' }
                ]
              }
            }
          };
        }

        // Mental health responses
        if (lowerMessage.includes('anxiety') || lowerMessage.includes('anxious') || lowerMessage.includes('panic')) {
          return {
            success: true,
            data: {
              message: "💙 I understand you're experiencing anxiety, which can be really challenging. You're not alone in this.\n\n🌬️ **Try this breathing exercise right now**:\n• Breathe in slowly for 4 counts\n• Hold for 4 counts\n• Breathe out slowly for 6 counts\n• Repeat 5 times\n\n🧘‍♀️ **Other helpful techniques**:\n• 5-4-3-2-1 grounding (name 5 things you see, 4 you hear, 3 you touch, 2 you smell, 1 you taste)\n• Progressive muscle relaxation\n• Gentle movement or walking\n\n💚 Consider speaking with a mental health professional for ongoing support.",
              confidence: 0.88,
              medicalTopics: ['anxiety', 'mental_health', 'coping_strategies'],
              recommendations: [
                'Practice the 4-4-6 breathing technique',
                'Try the 5-4-3-2-1 grounding exercise',
                'Consider professional counseling',
                'Regular exercise can help manage anxiety'
              ],
              disclaimers: ['This AI provides general information only. For persistent anxiety, please consult a healthcare provider.'],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('depression') || lowerMessage.includes('depressed') || lowerMessage.includes('sad')) {
          return {
            success: true,
            data: {
              message: "💙 I'm sorry you're feeling this way. Depression is real and treatable, and you're brave for reaching out.\n\n🌅 **Small steps that can help**:\n• Try to maintain a simple daily routine\n• Get some sunlight each day, even for 10 minutes\n• Connect with one supportive person\n• Do one small thing you used to enjoy\n• Be gentle with yourself\n\n🤝 **Remember**: Depression is not your fault, and with proper support, you can feel better. Consider reaching out to a mental health professional.\n\n💚 You matter, and your feelings are valid.",
              confidence: 0.85,
              medicalTopics: ['depression', 'mental_health', 'mood_disorders'],
              recommendations: [
                'Maintain a simple daily routine',
                'Get sunlight and fresh air daily',
                'Connect with supportive people',
                'Consider professional counseling',
                'Practice self-compassion'
              ],
              disclaimers: ['Depression is treatable with professional help. This information is not a substitute for medical advice.'],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        // Physical health responses
        if (lowerMessage.includes('headache') || lowerMessage.includes('head pain')) {
          return {
            success: true,
            data: {
              message: "🤕 Headaches can be really uncomfortable. Here are some things that might help:\n\n💧 **Immediate relief**:\n• Rest in a quiet, dark room\n• Apply a cold or warm compress\n• Stay hydrated - drink water slowly\n• Try gentle neck stretches\n• Consider over-the-counter pain relief if appropriate\n\n⚠️ **See a doctor if**:\n• Sudden, severe headache\n• Headache with fever, stiff neck, or vision changes\n• Frequent or worsening headaches\n\n📝 Keep a headache diary to identify triggers (stress, foods, sleep patterns).",
              confidence: 0.82,
              medicalTopics: ['headache', 'pain_management', 'symptoms'],
              recommendations: [
                'Rest in a quiet, dark room',
                'Stay well hydrated',
                'Apply cold or warm compress',
                'Try gentle stretches',
                'Keep a headache diary'
              ],
              disclaimers: ['Seek medical attention for severe or frequent headaches. This is general information only.'],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('stress') || lowerMessage.includes('stressed')) {
          return {
            success: true,
            data: {
              message: "😌 Stress is normal, but chronic stress can impact your health. Let's work on some strategies:\n\n🧘‍♀️ **Quick stress relief**:\n• Take 5 deep breaths\n• Do a 2-minute meditation\n• Step outside for fresh air\n• Listen to calming music\n• Call a supportive friend\n\n💪 **Long-term strategies**:\n• Regular exercise (even 10 minutes helps)\n• Adequate sleep (7-9 hours)\n• Set healthy boundaries\n• Practice saying 'no' when needed\n• Consider talking to a counselor\n\nWhat's your biggest source of stress right now?",
              confidence: 0.80,
              medicalTopics: ['stress_management', 'mental_health', 'wellness'],
              recommendations: [
                'Practice deep breathing exercises',
                'Get regular physical exercise',
                'Maintain good sleep habits',
                'Set healthy boundaries',
                'Consider professional support'
              ],
              disclaimers: ['Chronic stress can impact health. Consider professional help if stress is overwhelming.'],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        // Additional comprehensive health responses
        if (lowerMessage.includes('diabetes') || lowerMessage.includes('blood sugar') || lowerMessage.includes('glucose')) {
          return {
            success: true,
            data: {
              message: "🩺 Diabetes management is crucial for your health. Here's some guidance:\n\n📊 **Blood sugar management**:\n• Monitor levels as prescribed\n• Take medications consistently\n• Follow your meal plan\n• Stay hydrated\n• Exercise regularly (with doctor approval)\n\n🍎 **Dietary tips**:\n• Choose complex carbohydrates\n• Include lean proteins\n• Eat regular, balanced meals\n• Limit sugary drinks and snacks\n• Work with a dietitian if possible\n\n⚠️ **Seek immediate care for**:\n• Blood sugar >400 mg/dL or <70 mg/dL\n• Persistent vomiting\n• Difficulty breathing\n• Confusion or loss of consciousness\n\n💡 Regular check-ups with your healthcare team are essential for optimal diabetes management.",
              confidence: 0.88,
              medicalTopics: ['diabetes', 'blood_sugar', 'chronic_disease_management'],
              recommendations: [
                'Monitor blood sugar regularly',
                'Take medications as prescribed',
                'Follow a balanced meal plan',
                'Exercise with medical approval',
                'Attend regular medical check-ups'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('blood pressure') || lowerMessage.includes('hypertension') || lowerMessage.includes('bp')) {
          return {
            success: true,
            data: {
              message: "💓 Blood pressure management is vital for heart health. Here's how to help control it:\n\n🧂 **Lifestyle modifications**:\n• Reduce sodium intake (<2300mg/day)\n• Maintain healthy weight\n• Exercise regularly (30 min, 5 days/week)\n• Limit alcohol consumption\n• Quit smoking if applicable\n\n🥗 **DASH diet principles**:\n• Eat fruits and vegetables\n• Choose whole grains\n• Include lean proteins\n• Limit processed foods\n• Stay hydrated\n\n📱 **Monitoring tips**:\n• Check BP at same time daily\n• Use proper cuff size\n• Rest 5 minutes before measuring\n• Keep a log for your doctor\n\n⚠️ **Seek immediate care if BP >180/120 with symptoms like chest pain, shortness of breath, or severe headache.**",
              confidence: 0.87,
              medicalTopics: ['hypertension', 'cardiovascular_health', 'lifestyle_medicine'],
              recommendations: [
                'Follow DASH diet principles',
                'Exercise regularly with approval',
                'Monitor blood pressure consistently',
                'Take medications as prescribed',
                'Maintain healthy weight'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('medication') || lowerMessage.includes('pills') || lowerMessage.includes('drug interaction')) {
          return {
            success: true,
            data: {
              message: "💊 Medication safety is very important. Here's essential guidance:\n\n✅ **Safe medication practices**:\n• Take exactly as prescribed\n• Don't skip or double doses\n• Complete full course of antibiotics\n• Store medications properly\n• Check expiration dates regularly\n\n⚠️ **Drug interactions**:\n• Always tell doctors about ALL medications\n• Include supplements and herbal remedies\n• Use one pharmacy when possible\n• Ask pharmacist about interactions\n• Read medication guides carefully\n\n🚨 **Contact healthcare provider if**:\n• Severe side effects occur\n• Allergic reactions (rash, swelling, difficulty breathing)\n• Medications aren't working as expected\n• You want to stop or change medications\n\n💡 **Tip**: Keep an updated medication list with you at all times, including dosages and frequencies.",
              confidence: 0.85,
              medicalTopics: ['medication_safety', 'drug_interactions', 'pharmacy'],
              recommendations: [
                'Take medications exactly as prescribed',
                'Keep updated medication list',
                'Use one pharmacy consistently',
                'Ask about drug interactions',
                'Report side effects to healthcare provider'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        // Women's health responses
        if (lowerMessage.includes('period') || lowerMessage.includes('menstrual') || lowerMessage.includes('cramps')) {
          return {
            success: true,
            data: {
              message: "🌸 Menstrual health is an important part of overall wellness. Here's some guidance:\n\n💊 **For menstrual cramps**:\n• Apply heat to lower abdomen\n• Take ibuprofen or naproxen as directed\n• Try gentle exercise or yoga\n• Stay hydrated\n• Consider magnesium supplements\n\n📅 **Cycle tracking**:\n• Note cycle length and flow\n• Track symptoms and mood changes\n• Use apps or calendar for monitoring\n• Share patterns with healthcare provider\n\n⚠️ **See a doctor if**:\n• Periods suddenly become very heavy or painful\n• Cycles are very irregular\n• Severe pain that interferes with daily life\n• Bleeding between periods\n• No period for 3+ months (if not pregnant)\n\n💡 Normal cycles range from 21-35 days. Every person's cycle is different!",
              confidence: 0.85,
              medicalTopics: ['womens_health', 'menstrual_health', 'reproductive_health'],
              recommendations: [
                'Track your menstrual cycle regularly',
                'Use heat therapy for cramps',
                'Stay active with gentle exercise',
                'Maintain good nutrition',
                'Consult healthcare provider for concerns'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('pregnancy') || lowerMessage.includes('pregnant') || lowerMessage.includes('expecting')) {
          return {
            success: true,
            data: {
              message: "🤱 Pregnancy is an exciting time! Here's essential guidance for a healthy pregnancy:\n\n🥗 **Nutrition during pregnancy**:\n• Take prenatal vitamins with folic acid\n• Eat variety of fruits and vegetables\n• Include lean proteins and whole grains\n• Avoid alcohol, raw fish, and high-mercury fish\n• Limit caffeine to 200mg/day\n\n💪 **Staying healthy**:\n• Get regular prenatal care\n• Exercise as approved by your doctor\n• Get adequate sleep\n• Manage stress\n• Avoid smoking and secondhand smoke\n\n🚨 **Contact your healthcare provider immediately for**:\n• Severe abdominal pain\n• Heavy bleeding\n• Severe headaches or vision changes\n• Persistent vomiting\n• Decreased fetal movement (after 28 weeks)\n\n💡 Regular prenatal visits are crucial for monitoring both your health and baby's development.",
              confidence: 0.90,
              medicalTopics: ['pregnancy', 'prenatal_care', 'maternal_health'],
              recommendations: [
                'Attend all prenatal appointments',
                'Take prenatal vitamins daily',
                'Maintain healthy diet and exercise',
                'Avoid harmful substances',
                'Monitor for warning signs'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('child') || lowerMessage.includes('baby') || lowerMessage.includes('infant') || lowerMessage.includes('toddler')) {
          return {
            success: true,
            data: {
              message: "👶 Children's health requires special attention. Here's important guidance:\n\n🌡️ **Fever in children**:\n• Normal: 98.6°F (37°C) orally\n• Low-grade fever: 100.4-102°F (38-39°C)\n• Call doctor if under 3 months with any fever\n• For older children: fever >102°F or lasting >3 days\n\n💉 **Vaccination schedule**:\n• Follow recommended immunization schedule\n• Keep vaccination records updated\n• Discuss any concerns with pediatrician\n\n🍎 **Nutrition for children**:\n• Offer variety of healthy foods\n• Limit sugary drinks and snacks\n• Encourage water intake\n• Make mealtimes positive\n\n⚠️ **Emergency signs in children**:\n• Difficulty breathing\n• Severe dehydration\n• Unusual drowsiness or irritability\n• Persistent vomiting\n• High fever with rash\n\n💡 Trust your parental instincts - if something seems wrong, contact your pediatrician.",
              confidence: 0.88,
              medicalTopics: ['pediatric_health', 'child_development', 'parenting'],
              recommendations: [
                'Follow vaccination schedule',
                'Monitor growth and development',
                'Provide balanced nutrition',
                'Ensure adequate sleep',
                'Trust parental instincts about health concerns'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        // Enhanced symptom-specific responses
        if (lowerMessage.includes('fever') || lowerMessage.includes('temperature')) {
          return {
            success: true,
            data: {
              message: "🌡️ Fever can be concerning, but it's often your body's way of fighting infection.\n\n🏠 **Home care for mild fever (under 39°C/102°F)**:\n• Rest and stay hydrated\n• Take paracetamol or ibuprofen as directed\n• Use cool compresses on forehead\n• Wear light clothing\n• Monitor temperature regularly\n\n🚨 **Seek immediate medical care if**:\n• Fever over 39°C (102°F) or persists >3 days\n• Difficulty breathing or chest pain\n• Severe headache or neck stiffness\n• Persistent vomiting or dehydration\n• Confusion or unusual behavior\n\n📞 Don't hesitate to contact a healthcare provider if you're concerned.",
              confidence: 0.85,
              medicalTopics: ['fever', 'infection', 'symptom_management'],
              recommendations: [
                'Monitor temperature regularly',
                'Stay well hydrated',
                'Rest and avoid strenuous activity',
                'Take appropriate fever reducers',
                'Seek medical care if fever is high or persistent'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('cough') || lowerMessage.includes('coughing')) {
          return {
            success: true,
            data: {
              message: "😷 Coughs can have many causes. Let me help you understand when to be concerned:\n\n🍯 **For dry cough**:\n• Honey (1-2 teaspoons) can soothe throat\n• Stay hydrated with warm liquids\n• Use a humidifier or breathe steam\n• Avoid irritants like smoke\n\n💧 **For productive cough**:\n• Stay hydrated to thin mucus\n• Try warm salt water gargles\n• Sleep with head elevated\n\n⚠️ **See a doctor if**:\n• Cough persists >3 weeks\n• Blood in sputum\n• High fever or difficulty breathing\n• Chest pain or wheezing\n• Cough interferes with sleep\n\nWhat type of cough are you experiencing?",
              confidence: 0.82,
              medicalTopics: ['cough', 'respiratory_symptoms', 'home_remedies'],
              recommendations: [
                'Stay hydrated with warm fluids',
                'Use honey for throat soothing',
                'Avoid smoke and irritants',
                'Monitor for concerning symptoms',
                'See doctor if persistent or severe'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('stomach') || lowerMessage.includes('nausea') || lowerMessage.includes('vomiting')) {
          return {
            success: true,
            data: {
              message: "🤢 Stomach issues can be really uncomfortable. Here's how to manage them:\n\n🥤 **For nausea/vomiting**:\n• Sip clear fluids slowly (water, ginger tea)\n• Try the BRAT diet (bananas, rice, applesauce, toast)\n• Avoid dairy, fatty, or spicy foods\n• Rest and avoid strong odors\n• Ginger can help reduce nausea\n\n💊 **For stomach pain**:\n• Apply heat pad to abdomen\n• Avoid NSAIDs on empty stomach\n• Eat small, frequent meals\n\n🚨 **Seek immediate care if**:\n• Severe abdominal pain\n• Blood in vomit or stool\n• Signs of dehydration\n• High fever with stomach pain\n• Unable to keep fluids down >24 hours\n\nHow long have you been experiencing these symptoms?",
              confidence: 0.83,
              medicalTopics: ['gastrointestinal', 'nausea', 'stomach_pain'],
              recommendations: [
                'Stay hydrated with clear fluids',
                'Try the BRAT diet',
                'Use ginger for nausea relief',
                'Apply heat for comfort',
                'Monitor for severe symptoms'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('sleep') || lowerMessage.includes('insomnia') || lowerMessage.includes('tired')) {
          return {
            success: true,
            data: {
              message: "😴 Good sleep is crucial for health. Let's work on improving your sleep quality:\n\n🌙 **Sleep hygiene tips**:\n• Keep consistent sleep/wake times\n• Create a relaxing bedtime routine\n• Keep bedroom cool, dark, and quiet\n• Avoid screens 1 hour before bed\n• No caffeine after 2 PM\n\n🧘‍♀️ **Relaxation techniques**:\n• Progressive muscle relaxation\n• Deep breathing exercises\n• Gentle stretching or yoga\n• Meditation or mindfulness\n\n⚠️ **Consider seeing a doctor if**:\n• Chronic insomnia (>3 weeks)\n• Loud snoring or breathing stops\n• Excessive daytime sleepiness\n• Sleep doesn't feel refreshing\n\n💡 Most adults need 7-9 hours of sleep per night. What's your current sleep pattern like?",
              confidence: 0.80,
              medicalTopics: ['sleep_disorders', 'insomnia', 'sleep_hygiene'],
              recommendations: [
                'Maintain consistent sleep schedule',
                'Create relaxing bedtime routine',
                'Optimize sleep environment',
                'Practice relaxation techniques',
                'Limit caffeine and screen time'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (lowerMessage.includes('pain') || lowerMessage.includes('hurt') || lowerMessage.includes('ache')) {
          return {
            success: true,
            data: {
              message: "😣 I understand you're experiencing pain. Let me help you manage it safely:\n\n🩹 **General pain management**:\n• Rest the affected area\n• Apply ice for acute injuries (first 48 hours)\n• Use heat for muscle tension/chronic pain\n• Over-the-counter pain relievers as directed\n• Gentle movement when possible\n\n🧘‍♀️ **Non-medication approaches**:\n• Deep breathing and relaxation\n• Gentle stretching or yoga\n• Distraction techniques\n• Massage or physical therapy\n\n🚨 **Seek immediate care for**:\n• Severe, sudden pain\n• Chest pain or difficulty breathing\n• Signs of infection (fever, redness, swelling)\n• Pain after injury or trauma\n• Pain with numbness or weakness\n\nCan you tell me more about where the pain is and how it started?",
              confidence: 0.85,
              medicalTopics: ['pain_management', 'acute_pain', 'chronic_pain'],
              recommendations: [
                'Use appropriate ice or heat therapy',
                'Take pain relievers as directed',
                'Try gentle movement and stretching',
                'Practice relaxation techniques',
                'Monitor for warning signs'
              ],
              isEmergency: false,
              timestamp: new Date().toISOString()
            }
          };
        }

        // Default response
        return {
          success: true,
          data: {
            message: `💬 Hello! I'm your AI health assistant, trained to provide helpful medical guidance and support.\n\n🩺 **I can help with**:\n• Symptom assessment and advice\n• Mental health support (anxiety, depression, stress)\n• Medication information and interactions\n• Emergency resource guidance\n• Wellness and prevention tips\n• Chronic condition management\n\n💡 **Try asking me about**:\n• "I have a headache and fever"\n• "I'm feeling anxious about my health"\n• "What should I do for a cough?"\n• "I can't sleep well lately"\n• "I'm experiencing stomach pain"\n\n🔒 **Your privacy is protected** - all conversations are confidential.\n\n❓ What health concern can I help you with today?`,
            confidence: 0.75,
            medicalTopics: ['general_health', 'wellness', 'medical_guidance'],
            recommendations: [
              'Share your specific symptoms or concerns',
              'Be as detailed as possible for better guidance',
              'Remember this is for general information only',
              'Always consult healthcare providers for serious concerns'
            ],
            disclaimers: [
              'This AI provides general health information and is not a substitute for professional medical advice.',
              'Always consult with qualified healthcare providers for medical concerns.',
              'In emergencies, contact your local emergency services immediately (10177 in South Africa).'
            ],
            isEmergency: false,
            timestamp: new Date().toISOString()
          }
        };
      };

      const fallbackResponse = generateMockResponse(message);

      return fallbackResponse;
    }
  };

  // Enhanced Chat Functions
  const saveConversationHistory = async (messages: Message[]) => {
    try {
      await AsyncStorage.setItem(`chat_session_${currentSessionId}`, JSON.stringify(messages));
      await AsyncStorage.setItem('conversation_history', JSON.stringify(messages));
    } catch (error) {
      console.log('Error saving conversation:', error);
    }
  };

  const loadConversationHistory = async () => {
    try {
      const saved = await AsyncStorage.getItem(`chat_session_${currentSessionId}`);
      if (saved) {
        const savedMessages = JSON.parse(saved);
        setMessages(savedMessages);
        setConversationHistory(savedMessages);
      }
    } catch (error) {
      console.log('Error loading conversation:', error);
    }
  };

  const createNewChatSession = () => {
    const newSessionId = `session_${Date.now()}`;
    setCurrentSessionId(newSessionId);
    setMessages([{
      id: '1',
      text: 'Hello! I\'m your AI health assistant. How can I help you today?',
      sender: 'ai',
      timestamp: new Date(),
    }]);
  };

  const simulateTyping = async (duration: number = 2000) => {
    setIsTyping(true);
    await new Promise(resolve => setTimeout(resolve, duration));
    setIsTyping(false);
  };

  // Stable input handler to prevent keyboard dismissal
  const handleInputChange = React.useCallback((text: string) => {
    setInputText(text);
  }, []);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const messageId = Date.now().toString();
    const userMessage: Message = {
      id: messageId,
      text: inputText.trim(),
      sender: 'user',
      timestamp: new Date(),
    };

    // Add user message immediately
    setMessages(prev => [...prev, userMessage]);
    setMessageDeliveryStatus(prev => ({ ...prev, [messageId]: 'sending' }));
    setInputText('');
    setIsLoading(true);
    setLastMessageTime(new Date());

    // Save conversation
    const updatedMessages = [...messages, userMessage];
    await saveConversationHistory(updatedMessages);

    try {
      // Simulate typing indicator
      await simulateTyping(1500);

      const response = await sendChatMessage(userMessage.text);

      // Mark user message as delivered
      setMessageDeliveryStatus(prev => ({ ...prev, [messageId]: 'delivered' }));

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response.data.message,
        sender: 'ai',
        timestamp: new Date(),
        isEmergency: response.data.isEmergency,
        confidence: response.data.confidence,
        medicalTopics: response.data.medicalTopics,
        recommendations: response.data.recommendations,
      };

      setMessages(prev => [...prev, aiMessage]);

      // Save updated conversation
      const finalMessages = [...updatedMessages, aiMessage];
      await saveConversationHistory(finalMessages);

      // Add points for interaction
      addPoints(5);

      // Handle emergency detection with enhanced UI
      if (response.data.isEmergency) {
        setShowEmergencyModal(true);
        Alert.alert(
          '🚨 Emergency Detected',
          `Emergency level: ${response.data.emergencyLevel?.toUpperCase()}\n\nImmediate support resources:\n• Crisis Helpline: **********\n• Emergency Services: 10177\n• Suicide Prevention: **********`,
          [
            { text: 'Get Help Now', style: 'default', onPress: () => setShowEmergencyModal(true) },
            { text: 'Continue Chat', style: 'cancel' }
          ]
        );
      }

      // Check for achievements
      checkChatAchievements(finalMessages.length);

    } catch (error) {
      console.error('Failed to send message:', error);

      // Mark message as failed
      setMessageDeliveryStatus(prev => ({ ...prev, [messageId]: 'failed' }));

      // Enhanced error handling with fallback responses
      const fallbackResponses = [
        "I'm having trouble connecting right now, but I'm still here to help. Can you tell me more about what you're experiencing?",
        "Connection issues detected. While I work on reconnecting, remember that if this is urgent, please contact emergency services at 10177.",
        "I'm experiencing technical difficulties. For immediate support, you can reach the Crisis Helpline at **********.",
      ];

      const randomFallback = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];

      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        text: `${randomFallback}\n\n🔧 Technical note: ${error.message}`,
        sender: 'ai',
        timestamp: new Date(),
        isError: true,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const checkChatAchievements = (messageCount: number) => {
    if (messageCount >= 10) {
      setAchievements(prev => prev.map(achievement => {
        if (achievement.id === 'chat_starter' && !achievement.unlocked) {
          addPoints(25);
          return { ...achievement, unlocked: true, unlockedDate: new Date() };
        }
        return achievement;
      }));
    }
  };

  // Splash Screen
  const SplashScreen = () => (
    <SafeAreaView style={styles.splashContainer}>
      <StatusBar barStyle="light-content" backgroundColor="#22C55E" />
      <View style={styles.splashContent}>
        <View style={styles.splashLogoContainer}>
          <View style={styles.splashLogo}>
            <Text style={styles.splashLogoText}>🏥</Text>
          </View>
          <Text style={styles.splashTitle}>Mobile Spo</Text>
          <Text style={styles.splashSubtitle}>Your AI Health Assistant</Text>
        </View>

        <View style={styles.splashMessageContainer}>
          <Text style={styles.splashWelcomeText}>Welcome to your</Text>
          <Text style={styles.splashMainMessage}>Personal Health Journey</Text>
          <Text style={styles.splashDescription}>
            Get personalized health insights, track your wellness, and connect with a supportive community
          </Text>
        </View>

        <View style={styles.splashFooter}>
          <View style={styles.loadingIndicator}>
            <View style={styles.loadingDot} />
            <View style={[styles.loadingDot, styles.loadingDotDelay1]} />
            <View style={[styles.loadingDot, styles.loadingDotDelay2]} />
          </View>
          <Text style={styles.splashLoadingText}>Preparing your experience...</Text>
        </View>
      </View>
    </SafeAreaView>
  );

  // Welcome Screen
  const WelcomeScreen = () => (
    <SafeAreaView style={styles.welcomeScreenContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <ScrollView contentContainerStyle={styles.welcomeContainer} showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <View style={styles.welcomeHeader}>
          <View style={styles.welcomeLogo}>
            <Text style={styles.welcomeLogoText}>🏥</Text>
          </View>
          <Text style={styles.welcomeTitle}>Welcome to Mobile Spo</Text>
          <Text style={styles.welcomeSubtitle}>Your personalized AI health companion</Text>
          <Text style={styles.welcomeDescription}>
            Let's set up your experience to provide you with the most relevant health insights and support
          </Text>
        </View>

        {/* Configuration Section */}
        <View style={styles.welcomeConfigSection}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeSectionTitle}>🌍 Choose Your Language</Text>
            <Text style={styles.welcomeSectionSubtitle}>Select your preferred language for the app</Text>
            <View style={styles.welcomeOptionsGrid}>
              {['English', 'isiZulu', 'Sesotho', 'French'].map((lang) => (
                <TouchableOpacity
                  key={lang}
                  style={[
                    styles.welcomeOptionButton,
                    language === lang && styles.welcomeOptionButtonSelected
                  ]}
                  onPress={() => setLanguage(lang)}
                >
                  <Text style={[
                    styles.welcomeOptionText,
                    language === lang && styles.welcomeOptionTextSelected
                  ]}>
                    {lang}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeSectionTitle}>👤 Select Your Mode</Text>
            <Text style={styles.welcomeSectionSubtitle}>Choose the experience that fits you best</Text>
            <View style={styles.welcomeOptionsGrid}>
              {[
                { key: 'Youth', label: 'Youth', description: 'Ages 13-24' },
                { key: 'Adult', label: 'Adult', description: 'Ages 25+' }
              ].map((m) => (
                <TouchableOpacity
                  key={m.key}
                  style={[
                    styles.welcomeModeButton,
                    mode === m.key && styles.welcomeModeButtonSelected
                  ]}
                  onPress={() => setMode(m.key)}
                >
                  <Text style={[
                    styles.welcomeModeText,
                    mode === m.key && styles.welcomeModeTextSelected
                  ]}>
                    {m.label}
                  </Text>
                  <Text style={[
                    styles.welcomeModeDescription,
                    mode === m.key && styles.welcomeModeDescriptionSelected
                  ]}>
                    {m.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Action Section */}
        <View style={styles.welcomeActionSection}>
          <TouchableOpacity
            style={styles.welcomeContinueButton}
            onPress={() => setCurrentScreen('home')}
          >
            <Text style={styles.welcomeContinueButtonText}>Start Your Health Journey</Text>
            <Text style={styles.welcomeContinueButtonIcon}>→</Text>
          </TouchableOpacity>

          <View style={styles.welcomePrivacySection}>
            <Text style={styles.welcomePrivacyIcon}>🔒</Text>
            <Text style={styles.welcomePrivacyText}>
              Your health data stays private and secure on your device. We prioritize your privacy and never share your personal information.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );

  // Enhanced Home Screen
  const HomeScreen = () => (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <ScrollView contentContainerStyle={styles.homeContainer}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={styles.smallLogo}>
              <Text style={styles.smallLogoText}>🏥</Text>
            </View>
            <View>
              <Text style={styles.headerTitle}>Mobile Spo</Text>
              <Text style={styles.headerSubtitle}>{getGreeting()}!</Text>
            </View>
          </View>
          <View style={styles.headerRight}>
            <View style={styles.pointsContainer}>
              <Text style={styles.pointsText}>⭐ {userPoints}</Text>
            </View>
            <TouchableOpacity
              style={styles.profileButton}
              onPress={() => setCurrentScreen('profile')}
            >
              <Text style={styles.profileButtonText}>👤</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Streak & Achievement Banner */}
        <View style={styles.streakBanner}>
          <View style={styles.streakInfo}>
            <Text style={styles.streakIcon}>🔥</Text>
            <View>
              <Text style={styles.streakNumber}>{userStreak.currentStreak}</Text>
              <Text style={styles.streakLabel}>Day Streak</Text>
            </View>
          </View>
          <View style={styles.achievementPreview}>
            {achievements.filter(a => a.unlocked).length > 0 && (
              <TouchableOpacity style={styles.achievementBadge}>
                <Text style={styles.achievementIcon}>🏆</Text>
                <Text style={styles.achievementCount}>{achievements.filter(a => a.unlocked).length}</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Daily Did You Know */}
        {dailyFact && (
          <TouchableOpacity
            style={styles.didYouKnowCard}
            onPress={() => setShowDidYouKnowModal(true)}
          >
            <View style={styles.didYouKnowHeader}>
              <Text style={styles.didYouKnowTitle}>💡 Did You Know?</Text>
              <Text style={styles.funLevelIndicator}>
                {'⭐'.repeat(dailyFact.funLevel)}
              </Text>
            </View>
            <Text style={styles.didYouKnowFact} numberOfLines={2}>
              {dailyFact.emoji} {dailyFact.fact}
            </Text>
            <Text style={styles.didYouKnowCta}>Tap to learn more & get your daily tip!</Text>
          </TouchableOpacity>
        )}

        {/* Quick Health Check */}
        {showQuickHealthCheck && (
          <View style={styles.quickCheckCard}>
            <Text style={styles.cardTitle}>Quick Health Check</Text>
            <Text style={styles.questionText}>How are you feeling today?</Text>

            <View style={styles.moodSelector}>
              {moodEmojis.map((emoji, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.moodButton,
                    currentMood === index + 1 && styles.moodButtonSelected
                  ]}
                  onPress={() => {
                    setCurrentMood(index + 1);
                    setShowQuickHealthCheck(false);
                  }}
                >
                  <Text style={styles.moodEmoji}>{emoji}</Text>
                  <Text style={styles.moodLabel}>{moodLabels[index]}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}



        {/* Main Actions */}
        <View style={styles.actionGrid}>
          <TouchableOpacity
            style={[styles.actionCard, styles.primaryCard]}
            onPress={() => setCurrentScreen('chat')}
          >
            <Text style={styles.actionIcon}>💬</Text>
            <Text style={[styles.actionTitle, { color: '#ffffff' }]}>AI Health Chat</Text>
            <Text style={[styles.actionSubtitle, { color: '#ffffff', opacity: 0.9 }]}>Get instant health advice</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => setCurrentScreen('journal')}
          >
            <Text style={styles.actionIcon}>📝</Text>
            <Text style={styles.actionTitle}>Health Journal</Text>
            <Text style={styles.actionSubtitle}>Track symptoms & mood</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => setCurrentScreen('appointments')}
          >
            <Text style={styles.actionIcon}>📅</Text>
            <Text style={styles.actionTitle}>Appointments</Text>
            <Text style={styles.actionSubtitle}>Book & manage visits</Text>
          </TouchableOpacity>



          <TouchableOpacity
            style={[styles.actionCard, styles.emergencyCard]}
            onPress={() => setShowEmergencyModal(true)}
          >
            <Text style={styles.actionIcon}>🚨</Text>
            <Text style={styles.emergencyTitle}>Crisis Support</Text>
            <Text style={styles.emergencySubtitle}>Immediate help</Text>
          </TouchableOpacity>
        </View>

        {/* Recent Activity */}
        <View style={styles.recentCard}>
          <Text style={styles.cardTitle}>Recent Activity</Text>
          {journalEntries.length > 0 ? (
            <View style={styles.recentItem}>
              <Text style={styles.recentText}>
                Last journal entry: {formatDate(journalEntries[0].date)}
              </Text>
              <Text style={styles.recentMood}>
                Mood: {moodEmojis[journalEntries[0].mood - 1]} {moodLabels[journalEntries[0].mood - 1]}
              </Text>
            </View>
          ) : (
            <Text style={styles.recentText}>No recent activity. Start by adding a journal entry!</Text>
          )}
        </View>



        {/* Bottom Navigation */}
        <View style={styles.bottomNav}>
          <TouchableOpacity
            style={[styles.navItem, styles.navItemActive]}
            onPress={() => setCurrentScreen('home')}
          >
            <Text style={styles.navIcon}>🏠</Text>
            <Text style={[styles.navLabel, styles.navLabelActive]}>Home</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => setCurrentScreen('community')}
          >
            <Text style={styles.navIcon}>👥</Text>
            <Text style={styles.navLabel}>Community</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => setCurrentScreen('resources')}
          >
            <Text style={styles.navIcon}>📚</Text>
            <Text style={styles.navLabel}>Resources</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => setCurrentScreen('profile')}
          >
            <Text style={styles.navIcon}>👤</Text>
            <Text style={styles.navLabel}>Profile</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );

  // Enhanced Journal Screen
  const JournalScreen = () => {
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [showAnalytics, setShowAnalytics] = useState(false);

    const getFilteredEntries = () => {
      switch (selectedFilter) {
        case 'week':
          return journalEntries.filter(entry =>
            new Date(entry.date) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          );
        case 'month':
          return journalEntries.filter(entry =>
            new Date(entry.date) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          );
        case 'good':
          return journalEntries.filter(entry => entry.mood >= 4);
        case 'bad':
          return journalEntries.filter(entry => entry.mood <= 2);
        default:
          return journalEntries;
      }
    };

    const getMoodAnalytics = () => {
      if (journalEntries.length === 0) return null;

      const moodCounts = journalEntries.reduce((acc, entry) => {
        acc[entry.mood] = (acc[entry.mood] || 0) + 1;
        return acc;
      }, {});

      const avgMood = journalEntries.reduce((sum, entry) => sum + entry.mood, 0) / journalEntries.length;
      const mostCommonSymptoms = journalEntries
        .flatMap(entry => entry.symptoms)
        .reduce((acc, symptom) => {
          acc[symptom] = (acc[symptom] || 0) + 1;
          return acc;
        }, {});

      return { moodCounts, avgMood, mostCommonSymptoms };
    };

    const analytics = getMoodAnalytics();
    const filteredEntries = getFilteredEntries();

    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

        {/* Enhanced Header */}
        <View style={styles.journalHeader}>
          <TouchableOpacity onPress={() => setCurrentScreen('home')} style={styles.backButtonContainer}>
            <Text style={styles.backButton}>← Back</Text>
          </TouchableOpacity>
          <View style={styles.journalHeaderCenter}>
            <Text style={styles.screenTitle}>Health Journal</Text>
            <Text style={styles.journalSubtitle}>{journalEntries.length} entries</Text>
          </View>
          <View style={styles.journalHeaderActions}>
            <TouchableOpacity
              onPress={() => setShowAnalytics(!showAnalytics)}
              style={styles.analyticsButton}
            >
              <Text style={styles.analyticsIcon}>📊</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setShowNewJournalEntry(true)} style={styles.addEntryButton}>
              <Text style={styles.addEntryIcon}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Analytics Panel */}
        {showAnalytics && analytics && (
          <View style={styles.analyticsPanel}>
            <Text style={styles.analyticsPanelTitle}>📈 Your Health Insights</Text>
            <View style={styles.analyticsGrid}>
              <View style={styles.analyticsCard}>
                <Text style={styles.analyticsValue}>{analytics.avgMood.toFixed(1)}</Text>
                <Text style={styles.analyticsLabel}>Avg Mood</Text>
                <Text style={styles.analyticsEmoji}>{moodEmojis[Math.round(analytics.avgMood) - 1]}</Text>
              </View>
              <View style={styles.analyticsCard}>
                <Text style={styles.analyticsValue}>{journalEntries.length}</Text>
                <Text style={styles.analyticsLabel}>Total Entries</Text>
                <Text style={styles.analyticsEmoji}>📝</Text>
              </View>
              <View style={styles.analyticsCard}>
                <Text style={styles.analyticsValue}>{userStreak.currentStreak}</Text>
                <Text style={styles.analyticsLabel}>Day Streak</Text>
                <Text style={styles.analyticsEmoji}>🔥</Text>
              </View>
            </View>

            {Object.keys(analytics.mostCommonSymptoms).length > 0 && (
              <View style={styles.symptomsAnalytics}>
                <Text style={styles.symptomsAnalyticsTitle}>Most Common Symptoms:</Text>
                <View style={styles.symptomsAnalyticsGrid}>
                  {Object.entries(analytics.mostCommonSymptoms)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 3)
                    .map(([symptom, count]) => (
                      <View key={symptom} style={styles.symptomAnalyticsTag}>
                        <Text style={styles.symptomAnalyticsText}>{symptom}</Text>
                        <Text style={styles.symptomAnalyticsCount}>{count}x</Text>
                      </View>
                    ))}
                </View>
              </View>
            )}
          </View>
        )}

        {/* Filter Tabs */}
        <View style={styles.filterTabs}>
          {[
            { key: 'all', label: 'All', icon: '📅' },
            { key: 'week', label: 'Week', icon: '📆' },
            { key: 'month', label: 'Month', icon: '🗓️' },
            { key: 'good', label: 'Good Days', icon: '😊' },
            { key: 'bad', label: 'Tough Days', icon: '😔' }
          ].map(filter => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterTab,
                selectedFilter === filter.key && styles.filterTabActive
              ]}
              onPress={() => setSelectedFilter(filter.key)}
            >
              <Text style={styles.filterTabIcon}>{filter.icon}</Text>
              <Text style={[
                styles.filterTabText,
                selectedFilter === filter.key && styles.filterTabTextActive
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>



        <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
          {filteredEntries.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyIcon}>
                {selectedFilter === 'all' ? '📝' :
                 selectedFilter === 'good' ? '😊' :
                 selectedFilter === 'bad' ? '😔' : '📅'}
              </Text>
              <Text style={styles.emptyTitle}>
                {selectedFilter === 'all' ? 'Start Your Health Journey' :
                 selectedFilter === 'good' ? 'No Good Days Yet' :
                 selectedFilter === 'bad' ? 'No Tough Days Recorded' :
                 `No Entries This ${selectedFilter.charAt(0).toUpperCase() + selectedFilter.slice(1)}`}
              </Text>
              <Text style={styles.emptyText}>
                {selectedFilter === 'all'
                  ? 'Track your daily mood, symptoms, and health insights to better understand your wellbeing patterns.'
                  : 'Try adjusting your filter or add more journal entries to see insights here.'
                }
              </Text>
              {selectedFilter === 'all' && (
                <TouchableOpacity
                  style={styles.primaryButton}
                  onPress={() => setShowNewJournalEntry(true)}
                >
                  <Text style={styles.primaryButtonText}>Add First Entry</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <View style={styles.entriesContainer}>
              {filteredEntries.map((item, index) => (
                <View key={item.id} style={[styles.journalCard, styles.enhancedJournalCard]}>
                  {/* Entry Header */}
                  <View style={styles.journalCardHeader}>
                    <View style={styles.journalDateContainer}>
                      <Text style={styles.journalDate}>{formatDate(item.date)}</Text>
                      <Text style={styles.journalTime}>{formatTime(item.date)}</Text>
                    </View>
                    <View style={styles.moodDisplayLarge}>
                      <Text style={styles.moodEmojiLarge}>{moodEmojis[item.mood - 1]}</Text>
                      <Text style={styles.moodTextLarge}>{moodLabels[item.mood - 1]}</Text>
                      <View style={styles.moodRating}>
                        {[1, 2, 3, 4, 5].map(star => (
                          <Text key={star} style={[
                            styles.moodStar,
                            star <= item.mood && styles.moodStarActive
                          ]}>⭐</Text>
                        ))}
                      </View>
                    </View>
                  </View>

                  {/* Symptoms Section */}
                  {item.symptoms.length > 0 && (
                    <View style={styles.symptomsSection}>
                      <Text style={styles.sectionTitle}>🩺 Symptoms</Text>
                      <View style={styles.symptomsGrid}>
                        {item.symptoms.map((symptom, index) => (
                          <View key={index} style={styles.symptomTagEnhanced}>
                            <Text style={styles.symptomTextEnhanced}>{symptom}</Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  )}

                  {/* Notes Section */}
                  {item.notes && (
                    <View style={styles.notesSection}>
                      <Text style={styles.sectionTitle}>📝 Notes</Text>
                      <Text style={styles.notesTextEnhanced}>{item.notes}</Text>
                    </View>
                  )}

                  {/* Entry Actions */}
                  <View style={styles.entryActions}>
                    <TouchableOpacity
                      style={styles.entryAction}
                      onPress={() => shareJournalEntry(item.id)}
                    >
                      <Text style={styles.entryActionIcon}>📤</Text>
                      <Text style={styles.entryActionText}>Share</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.entryAction}
                      onPress={() => editJournalEntry(item.id)}
                    >
                      <Text style={styles.entryActionIcon}>✏️</Text>
                      <Text style={styles.entryActionText}>Edit</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.entryAction}
                      onPress={() => duplicateJournalEntry(item.id)}
                    >
                      <Text style={styles.entryActionIcon}>📋</Text>
                      <Text style={styles.entryActionText}>Copy</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.entryAction}
                      onPress={() => deleteJournalEntry(item.id)}
                    >
                      <Text style={styles.entryActionIcon}>🗑️</Text>
                      <Text style={styles.entryActionText}>Delete</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          )}
        </ScrollView>

      {/* Enhanced Journal Entry Modal */}
      <Modal
        visible={showJournalModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.enhancedModalHeader}>
            <TouchableOpacity
              onPress={() => setShowJournalModal(false)}
              style={styles.modalCancelButton}
            >
              <Text style={styles.modalCancel}>Cancel</Text>
            </TouchableOpacity>
            <View style={styles.modalTitleContainer}>
              <Text style={styles.modalTitle}>📝 New Journal Entry</Text>
              <Text style={styles.modalSubtitle}>Track your daily wellness</Text>
            </View>
            <TouchableOpacity
              onPress={addJournalEntry}
              style={styles.modalSaveButton}
            >
              <Text style={styles.modalSave}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {/* Enhanced Mood Selection */}
            <View style={styles.enhancedModalSection}>
              <View style={styles.sectionHeaderContainer}>
                <Text style={styles.modalSectionTitle}>😊 How are you feeling today?</Text>
                <Text style={styles.sectionDescription}>Select your overall mood</Text>
              </View>
              <View style={styles.enhancedMoodSelector}>
                {moodEmojis.map((emoji, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.enhancedMoodButton,
                      currentMood === index + 1 && styles.enhancedMoodButtonSelected
                    ]}
                    onPress={() => setCurrentMood(index + 1)}
                  >
                    <Text style={[
                      styles.moodEmojiLarge,
                      currentMood === index + 1 && styles.moodEmojiSelected
                    ]}>
                      {emoji}
                    </Text>
                    <Text style={[
                      styles.moodLabelEnhanced,
                      currentMood === index + 1 && styles.moodLabelSelected
                    ]}>
                      {moodLabels[index]}
                    </Text>
                    <View style={styles.moodRatingContainer}>
                      {[1, 2, 3, 4, 5].map(star => (
                        <Text key={star} style={[
                          styles.moodStarSmall,
                          star <= index + 1 && styles.moodStarActiveSmall
                        ]}>⭐</Text>
                      ))}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Enhanced Symptoms Selection */}
            <View style={styles.enhancedModalSection}>
              <View style={styles.sectionHeaderContainer}>
                <Text style={styles.modalSectionTitle}>🩺 Any symptoms today?</Text>
                <Text style={styles.sectionDescription}>
                  Select all that apply ({currentSymptoms.length} selected)
                </Text>
              </View>
              <View style={styles.enhancedSymptomsGrid}>
                {commonSymptoms.map((symptom) => (
                  <TouchableOpacity
                    key={symptom}
                    style={[
                      styles.enhancedSymptomButton,
                      currentSymptoms.includes(symptom) && styles.enhancedSymptomButtonSelected
                    ]}
                    onPress={() => toggleSymptom(symptom)}
                  >
                    <Text style={[
                      styles.enhancedSymptomButtonText,
                      currentSymptoms.includes(symptom) && styles.enhancedSymptomButtonTextSelected
                    ]}>
                      {symptom}
                    </Text>
                    {currentSymptoms.includes(symptom) && (
                      <Text style={styles.symptomCheckmark}>✓</Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Health Metrics Section */}
            <View style={styles.enhancedModalSection}>
              <View style={styles.sectionHeaderContainer}>
                <Text style={styles.modalSectionTitle}>📊 Health Metrics (Optional)</Text>
                <Text style={styles.sectionDescription}>Track additional health data</Text>
              </View>

              <View style={styles.metricsContainer}>
                <View style={styles.metricRow}>
                  <Text style={styles.metricLabel}>💧 Water Intake (glasses)</Text>
                  <View style={styles.metricCounter}>
                    <TouchableOpacity
                      style={styles.metricButton}
                      onPress={() => updateWaterIntake(-1)}
                    >
                      <Text style={styles.metricButtonText}>-</Text>
                    </TouchableOpacity>
                    <Text style={styles.metricValue}>{waterIntake}</Text>
                    <TouchableOpacity
                      style={styles.metricButton}
                      onPress={() => updateWaterIntake(1)}
                    >
                      <Text style={styles.metricButtonText}>+</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.metricRow}>
                  <Text style={styles.metricLabel}>😴 Sleep Hours</Text>
                  <View style={styles.metricCounter}>
                    <TouchableOpacity
                      style={styles.metricButton}
                      onPress={() => updateSleepHours(-0.5)}
                    >
                      <Text style={styles.metricButtonText}>-</Text>
                    </TouchableOpacity>
                    <Text style={styles.metricValue}>{sleepHours}</Text>
                    <TouchableOpacity
                      style={styles.metricButton}
                      onPress={() => updateSleepHours(0.5)}
                    >
                      <Text style={styles.metricButtonText}>+</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.metricRow}>
                  <Text style={styles.metricLabel}>🏃 Exercise Minutes</Text>
                  <View style={styles.metricCounter}>
                    <TouchableOpacity
                      style={styles.metricButton}
                      onPress={() => updateExerciseMinutes(-15)}
                    >
                      <Text style={styles.metricButtonText}>-</Text>
                    </TouchableOpacity>
                    <Text style={styles.metricValue}>{exerciseMinutes}</Text>
                    <TouchableOpacity
                      style={styles.metricButton}
                      onPress={() => updateExerciseMinutes(15)}
                    >
                      <Text style={styles.metricButtonText}>+</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>

            {/* Enhanced Notes Section */}
            <View style={styles.enhancedModalSection}>
              <View style={styles.sectionHeaderContainer}>
                <Text style={styles.modalSectionTitle}>📝 Additional Notes</Text>
                <Text style={styles.sectionDescription}>
                  Share your thoughts, observations, or anything else
                </Text>
              </View>
              <View style={styles.notesInputContainer}>
                <TextInput
                  style={styles.enhancedNotesInput}
                  value={journalNotes}
                  onChangeText={setJournalNotes}
                  placeholder="How was your day? Any thoughts, observations, or goals for tomorrow..."
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                  maxLength={500}
                />
                <Text style={styles.characterCountModal}>{journalNotes.length}/500</Text>
              </View>
            </View>

            {/* Quick Prompts */}
            <View style={styles.enhancedModalSection}>
              <Text style={styles.modalSectionTitle}>💭 Quick Prompts</Text>
              <View style={styles.quickPromptsContainer}>
                {[
                  "What made me smile today?",
                  "What challenged me today?",
                  "What am I grateful for?",
                  "How can I improve tomorrow?"
                ].map((prompt, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.quickPromptButton}
                    onPress={() => setJournalNotes(prev => prev + (prev ? '\n\n' : '') + prompt + ' ')}
                  >
                    <Text style={styles.quickPromptText}>{prompt}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Save Button */}
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.saveEntryButton}
                onPress={addJournalEntry}
              >
                <Text style={styles.saveEntryButtonText}>💾 Save Journal Entry</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Floating Action Button for New Entry */}
      <TouchableOpacity
        style={styles.floatingActionButton}
        onPress={() => setShowNewJournalEntry(true)}
      >
        <Text style={styles.floatingActionButtonText}>+</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
  };

  // New Journal Entry Modal
  const NewJournalEntryModal = () => {
    const availableSymptoms = [
      'Headache', 'Fatigue', 'Nausea', 'Dizziness', 'Anxiety', 'Stress',
      'Back Pain', 'Insomnia', 'Fever', 'Cough', 'Sore Throat', 'Muscle Aches',
      'Joint Pain', 'Shortness of Breath', 'Chest Pain', 'Stomach Pain'
    ];

    return (
      <Modal
        visible={showNewJournalEntry}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => {
              setShowNewJournalEntry(false);
              setEditingJournalEntry(null);
              setNewJournalData({
                mood: 3,
                symptoms: [],
                notes: '',
                medications: '',
                sleepHours: '',
                waterIntake: '',
                exerciseMinutes: '',
                stressLevel: 3,
                energyLevel: 3
              });
            }}>
              <Text style={styles.modalCloseButton}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {editingJournalEntry ? 'Edit Journal Entry' : '📝 New Journal Entry'}
            </Text>
            <View style={styles.modalHeaderSpacer} />
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {/* Mood Selection */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>😊 How are you feeling today?</Text>
              <View style={styles.moodSelectorGrid}>
                {moodEmojis.map((emoji, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.moodSelectorButton,
                      newJournalData.mood === index + 1 && styles.moodSelectorButtonSelected
                    ]}
                    onPress={() => handleJournalInputChange('mood', index + 1)}
                  >
                    <Text style={styles.moodSelectorEmoji}>{emoji}</Text>
                    <Text style={[
                      styles.moodSelectorLabel,
                      newJournalData.mood === index + 1 && styles.moodSelectorLabelSelected
                    ]}>
                      {moodLabels[index]}
                    </Text>
                    <View style={styles.moodRatingStars}>
                      {[1, 2, 3, 4, 5].map(star => (
                        <Text key={star} style={[
                          styles.moodStar,
                          star <= index + 1 && styles.moodStarActive
                        ]}>⭐</Text>
                      ))}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Symptoms Selection */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>
                🩺 Any symptoms today? ({newJournalData.symptoms.length} selected)
              </Text>
              <View style={styles.symptomsGrid}>
                {availableSymptoms.map((symptom) => (
                  <TouchableOpacity
                    key={symptom}
                    style={[
                      styles.symptomChip,
                      newJournalData.symptoms.includes(symptom) && styles.symptomChipSelected
                    ]}
                    onPress={() => toggleSymptom(symptom)}
                  >
                    <Text style={[
                      styles.symptomChipText,
                      newJournalData.symptoms.includes(symptom) && styles.symptomChipTextSelected
                    ]}>
                      {symptom}
                    </Text>
                    {newJournalData.symptoms.includes(symptom) && (
                      <Text style={styles.symptomCheckmark}>✓</Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Health Metrics */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>📊 Health Metrics (Optional)</Text>

              <View style={styles.metricsGrid}>
                <View style={styles.metricItem}>
                  <Text style={styles.metricLabel}>💧 Water (glasses)</Text>
                  <TextInput
                    key="water-intake-input"
                    style={styles.metricInput}
                    value={newJournalData.waterIntake}
                    onChangeText={(text) => handleJournalInputChange('waterIntake', text)}
                    placeholder="8"
                    keyboardType="numeric"
                    returnKeyType="next"
                    maxLength={2}
                    blurOnSubmit={false}
                    suppressContentEditableWarning={true}
                  />
                </View>

                <View style={styles.metricItem}>
                  <Text style={styles.metricLabel}>😴 Sleep (hours)</Text>
                  <TextInput
                    key="sleep-hours-input"
                    style={styles.metricInput}
                    value={newJournalData.sleepHours}
                    onChangeText={(text) => handleJournalInputChange('sleepHours', text)}
                    placeholder="8"
                    keyboardType="numeric"
                    returnKeyType="next"
                    maxLength={2}
                    blurOnSubmit={false}
                    suppressContentEditableWarning={true}
                  />
                </View>

                <View style={styles.metricItem}>
                  <Text style={styles.metricLabel}>🏃 Exercise (min)</Text>
                  <TextInput
                    key="exercise-minutes-input"
                    style={styles.metricInput}
                    value={newJournalData.exerciseMinutes}
                    onChangeText={(text) => handleJournalInputChange('exerciseMinutes', text)}
                    placeholder="30"
                    keyboardType="numeric"
                    returnKeyType="next"
                    maxLength={3}
                    blurOnSubmit={false}
                    suppressContentEditableWarning={true}
                  />
                </View>
              </View>

              {/* Stress and Energy Levels */}
              <View style={styles.levelSelectors}>
                <View style={styles.levelSelector}>
                  <Text style={styles.levelLabel}>😰 Stress Level</Text>
                  <View style={styles.levelButtons}>
                    {[1, 2, 3, 4, 5].map(level => (
                      <TouchableOpacity
                        key={level}
                        style={[
                          styles.levelButton,
                          newJournalData.stressLevel === level && styles.levelButtonSelected
                        ]}
                        onPress={() => handleJournalInputChange('stressLevel', level)}
                      >
                        <Text style={[
                          styles.levelButtonText,
                          newJournalData.stressLevel === level && styles.levelButtonTextSelected
                        ]}>
                          {level}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.levelSelector}>
                  <Text style={styles.levelLabel}>⚡ Energy Level</Text>
                  <View style={styles.levelButtons}>
                    {[1, 2, 3, 4, 5].map(level => (
                      <TouchableOpacity
                        key={level}
                        style={[
                          styles.levelButton,
                          newJournalData.energyLevel === level && styles.levelButtonSelected
                        ]}
                        onPress={() => handleJournalInputChange('energyLevel', level)}
                      >
                        <Text style={[
                          styles.levelButtonText,
                          newJournalData.energyLevel === level && styles.levelButtonTextSelected
                        ]}>
                          {level}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>
            </View>

            {/* Medications */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>💊 Medications (Optional)</Text>
              <TextInput
                key="medications-input"
                style={styles.formInput}
                value={newJournalData.medications}
                onChangeText={(text) => handleJournalInputChange('medications', text)}
                placeholder="List any medications taken today..."
                placeholderTextColor="#9CA3AF"
                autoCorrect={true}
                autoCapitalize="sentences"
                returnKeyType="next"
                blurOnSubmit={false}
                suppressContentEditableWarning={true}
              />
            </View>

            {/* Notes */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>📝 Additional Notes</Text>
              <TextInput
                key="notes-input"
                style={[styles.formInput, styles.formTextArea]}
                value={newJournalData.notes}
                onChangeText={(text) => handleJournalInputChange('notes', text)}
                placeholder="How was your day? Any thoughts, observations, or goals for tomorrow..."
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                maxLength={500}
                autoCorrect={true}
                autoCapitalize="sentences"
                returnKeyType="default"
                blurOnSubmit={false}
                suppressContentEditableWarning={true}
              />
              <Text style={styles.characterCount}>{newJournalData.notes.length}/500</Text>
            </View>

            {/* Save Button */}
            <TouchableOpacity
              style={styles.saveJournalButton}
              onPress={editingJournalEntry ? updateJournalEntry : addJournalEntry}
            >
              <Text style={styles.saveJournalButtonText}>
                {editingJournalEntry ? '✅ Update Entry' : '📝 Save Entry'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    );
  };

  // Enhanced Community Screen with Amazing Functionality
  const CommunityScreen = () => {
    const [communityView, setCommunityView] = useState('feed'); // feed, groups, mentors, events

    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#F0FDF4" />

        {/* Enhanced Header with Safety Features */}
        <View style={styles.communityScreenHeader}>
          <TouchableOpacity onPress={() => setCurrentScreen('home')}>
            <Text style={styles.backButton}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.screenTitle}>Safe Community</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.crisisButton} onPress={showCrisisResources}>
              <Text style={styles.crisisButtonText}>🆘</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.addPostButton}>
              <Text style={styles.addButton}>+ Share</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Community Safety Banner */}
        <View style={styles.safetyBanner}>
          <Text style={styles.safetyIcon}>🛡️</Text>
          <View style={styles.safetyTextContainer}>
            <Text style={styles.safetyTitle}>Safe Space Guarantee</Text>
            <Text style={styles.safetySubtext}>Moderated 24/7 • Anonymous options • Crisis support available</Text>
          </View>
        </View>

        {/* Navigation Tabs */}
        <View style={styles.communityNavTabs}>
          {[
            { key: 'feed', label: 'Feed', icon: '💬' },
            { key: 'groups', label: 'Groups', icon: '👥' },
            { key: 'mentors', label: 'Mentors', icon: '🤝' },
            { key: 'events', label: 'Events', icon: '📅' }
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.communityNavTab,
                communityView === tab.key && styles.communityNavTabActive
              ]}
              onPress={() => setCommunityView(tab.key)}
            >
              <Text style={styles.communityNavIcon}>{tab.icon}</Text>
              <Text style={[
                styles.communityNavText,
                communityView === tab.key && styles.communityNavTextActive
              ]}>{tab.label}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <ScrollView style={styles.screenContent}>
          {communityView === 'feed' && (
            <>
              {/* Post Categories */}
              <View style={styles.categoryTabs}>
                {[
                  { key: 'All', icon: '🌟', count: communityPosts.length },
                  { key: 'Support', icon: '🤗', count: communityPosts.filter(p => p.category === 'support').length },
                  { key: 'Questions', icon: '❓', count: communityPosts.filter(p => p.category === 'question').length },
                  { key: 'Success', icon: '🎉', count: communityPosts.filter(p => p.category === 'success').length },
                  { key: 'Crisis', icon: '🆘', count: 0 }
                ].map((tab) => (
                  <TouchableOpacity
                    key={tab.key}
                    style={[
                      styles.categoryTab,
                      selectedCommunityTab === tab.key && styles.categoryTabActive
                    ]}
                    onPress={() => setSelectedCommunityTab(tab.key)}
                  >
                    <Text style={styles.categoryTabIcon}>{tab.icon}</Text>
                    <Text style={styles.categoryTabText}>{tab.key}</Text>
                    <Text style={styles.categoryTabCount}>({tab.count})</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Community Stats */}
              <View style={styles.communityStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>1,247</Text>
                  <Text style={styles.statLabel}>Active Members</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>89</Text>
                  <Text style={styles.statLabel}>Online Now</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>24/7</Text>
                  <Text style={styles.statLabel}>Support Available</Text>
                </View>
              </View>

              {/* Enhanced Posts */}
              <View>
                {communityPosts.map((item) => (
                  <View key={item.id} style={styles.enhancedPostCard}>
                    <View style={styles.postHeader}>
                      <View style={styles.postAuthor}>
                        <View style={[styles.avatarPlaceholder, item.isAnonymous && styles.anonymousAvatar]}>
                          <Text style={styles.avatarText}>
                            {item.isAnonymous ? '🤗' : item.avatar || item.author.charAt(0)}
                          </Text>
                        </View>
                        <View style={styles.authorInfo}>
                          <View style={styles.authorNameRow}>
                            <Text style={styles.authorName}>
                              {item.isAnonymous ? 'Anonymous Community Member' : item.author}
                            </Text>
                            {item.isVerified && <Text style={styles.verifiedBadge}>✓</Text>}
                            {item.isProfessional && <Text style={styles.professionalBadge}>👩‍⚕️</Text>}
                          </View>
                          <Text style={styles.postTime}>{formatTime(item.timestamp)}</Text>
                          {item.location && <Text style={styles.locationText}>📍 {item.location}</Text>}
                        </View>
                      </View>
                      <TouchableOpacity onPress={() => reportPost(item.id)}>
                        <Text style={styles.reportButton}>⋯</Text>
                      </TouchableOpacity>
                    </View>

                    {/* Mood Indicator */}
                    <View style={[styles.moodIndicator, styles[`mood${item.mood}`]]}>
                      <Text style={styles.moodText}>
                        {item.mood === 'positive' && '😊 Feeling positive'}
                        {item.mood === 'seeking-help' && '🤲 Seeking support'}
                        {item.mood === 'supportive' && '💚 Offering support'}
                        {item.mood === 'encouraging' && '💪 Encouraging others'}
                        {item.mood === 'grateful' && '🙏 Feeling grateful'}
                        {item.mood === 'nurturing' && '🤱 Nurturing community'}
                      </Text>
                    </View>

                    {/* Category Badge */}
                    <View style={[styles.categoryBadge, styles[`category${item.category}`]]}>
                      <Text style={styles.categoryBadgeText}>
                        {item.category === 'success' && '🎉 Success Story'}
                        {item.category === 'question' && '❓ Seeking Advice'}
                        {item.category === 'support' && '🤗 Offering Support'}
                        {item.category === 'community-event' && '📅 Community Event'}
                        {item.category === 'milestone' && '🏆 Milestone'}
                        {item.category === 'support-group' && '👥 Support Group'}
                      </Text>
                    </View>

                    <Text style={styles.postContent}>{item.content}</Text>

                    {/* Tags */}
                    {item.tags && (
                      <View style={styles.tagsContainer}>
                        {item.tags.map((tag, index) => (
                          <View key={index} style={styles.tag}>
                            <Text style={styles.tagText}>#{tag}</Text>
                          </View>
                        ))}
                      </View>
                    )}

                    {/* Enhanced Actions */}
                    <View style={styles.postActions}>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => likePost(item.id)}
                      >
                        <Text style={styles.actionIcon}>💚</Text>
                        <Text style={styles.actionText}>{item.likes}</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => commentOnPost(item.id)}
                      >
                        <Text style={styles.actionIcon}>💬</Text>
                        <Text style={styles.actionText}>{item.comments}</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.supportActionButton}
                        onPress={() => supportPost(item.id)}
                      >
                        <Text style={styles.actionIcon}>🤝</Text>
                        <Text style={styles.supportActionText}>{item.supportCount} Support</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.shareButton}
                        onPress={() => Alert.alert('Share', 'Sharing supportive content helps others!')}
                      >
                        <Text style={styles.actionIcon}>📤</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            </>
          )}

          {/* Support Groups View */}
          {communityView === 'groups' && (
            <View>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>🛡️ Safe Support Groups</Text>
                <Text style={styles.sectionSubtitle}>Join moderated groups for peer support</Text>
              </View>

              {communityGroups.map((group) => (
                <View key={group.id} style={styles.groupCard}>
                  <View style={styles.groupHeader}>
                    <View style={styles.groupInfo}>
                      <Text style={styles.groupName}>{group.name}</Text>
                      <Text style={styles.groupDescription}>{group.description}</Text>
                      <View style={styles.groupMeta}>
                        <Text style={styles.groupMembers}>👥 {group.members} members</Text>
                        <Text style={styles.groupMeeting}>⏰ {group.meetingTime}</Text>
                        <Text style={styles.groupModerator}>👨‍⚕️ {group.moderator}</Text>
                      </View>
                    </View>
                    <View style={styles.groupPrivacy}>
                      <Text style={styles.privacyBadge}>
                        {group.isPrivate ? '🔒 Private' : '🌍 Public'}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.groupTags}>
                    {group.tags.map((tag, index) => (
                      <View key={index} style={styles.groupTag}>
                        <Text style={styles.groupTagText}>#{tag}</Text>
                      </View>
                    ))}
                  </View>

                  <TouchableOpacity
                    style={styles.joinGroupButton}
                    onPress={() => joinSupportGroup(group.id)}
                  >
                    <Text style={styles.joinGroupText}>Join Safe Group</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}

          {/* Peer Mentors View */}
          {communityView === 'mentors' && (
            <View>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>🤝 Peer Mentors & Professionals</Text>
                <Text style={styles.sectionSubtitle}>Connect with trained supporters</Text>
              </View>

              {peerMentors.map((mentor) => (
                <View key={mentor.id} style={styles.mentorCard}>
                  <View style={styles.mentorHeader}>
                    <View style={styles.mentorAvatar}>
                      <Text style={styles.mentorAvatarText}>{mentor.avatar}</Text>
                      <View style={[styles.onlineIndicator, mentor.isOnline && styles.onlineActive]} />
                    </View>
                    <View style={styles.mentorInfo}>
                      <View style={styles.mentorNameRow}>
                        <Text style={styles.mentorName}>{mentor.name}</Text>
                        {mentor.isProfessional && <Text style={styles.professionalBadge}>👩‍⚕️ Licensed</Text>}
                      </View>
                      <Text style={styles.mentorExperience}>{mentor.experience}</Text>
                      <View style={styles.mentorRating}>
                        <Text style={styles.ratingStars}>⭐⭐⭐⭐⭐</Text>
                        <Text style={styles.ratingText}>{mentor.rating}/5.0</Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.mentorSpecialties}>
                    {mentor.specialties.map((specialty, index) => (
                      <View key={index} style={styles.specialtyTag}>
                        <Text style={styles.specialtyText}>{specialty}</Text>
                      </View>
                    ))}
                  </View>

                  <View style={styles.mentorDetails}>
                    <Text style={styles.responseTime}>⚡ Responds {mentor.responseTime}</Text>
                    <Text style={styles.languages}>🗣️ {mentor.languages.join(', ')}</Text>
                  </View>

                  <TouchableOpacity
                    style={styles.connectButton}
                    onPress={() => connectWithMentor(mentor.id)}
                  >
                    <Text style={styles.connectButtonText}>
                      {mentor.isOnline ? '💬 Connect Now' : '📨 Send Message'}
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}

          {/* Community Events View */}
          {communityView === 'events' && (
            <View>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>📅 Community Events & Workshops</Text>
                <Text style={styles.sectionSubtitle}>Join healing activities and educational sessions</Text>
              </View>

              <View style={styles.eventCard}>
                <View style={styles.eventHeader}>
                  <Text style={styles.eventTitle}>🧘‍♀️ Mindfulness Monday</Text>
                  <Text style={styles.eventBadge}>Weekly</Text>
                </View>
                <Text style={styles.eventDescription}>
                  Join our weekly guided meditation session. Perfect for beginners and experienced practitioners.
                </Text>
                <View style={styles.eventDetails}>
                  <Text style={styles.eventTime}>⏰ Every Monday, 7:00 PM</Text>
                  <Text style={styles.eventLocation}>🌍 Virtual (Zoom)</Text>
                  <Text style={styles.eventHost}>👨‍⚕️ Led by Dr. Sarah M.</Text>
                </View>
                <TouchableOpacity style={styles.joinEventButton}>
                  <Text style={styles.joinEventText}>Join Session</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.eventCard}>
                <View style={styles.eventHeader}>
                  <Text style={styles.eventTitle}>🚶‍♂️ Walking for Wellness</Text>
                  <Text style={styles.eventBadge}>Saturday</Text>
                </View>
                <Text style={styles.eventDescription}>
                  Community walk in Johannesburg. Exercise, fresh air, and supportive conversations.
                </Text>
                <View style={styles.eventDetails}>
                  <Text style={styles.eventTime}>⏰ Saturday, 8:00 AM</Text>
                  <Text style={styles.eventLocation}>📍 Delta Park, Johannesburg</Text>
                  <Text style={styles.eventHost}>🤝 Organized by Thabo M.</Text>
                </View>
                <TouchableOpacity style={styles.joinEventButton}>
                  <Text style={styles.joinEventText}>Join Walk</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.eventCard}>
                <View style={styles.eventHeader}>
                  <Text style={styles.eventTitle}>💬 Mental Health First Aid Workshop</Text>
                  <Text style={styles.eventBadge}>Monthly</Text>
                </View>
                <Text style={styles.eventDescription}>
                  Learn how to support friends and family experiencing mental health challenges.
                </Text>
                <View style={styles.eventDetails}>
                  <Text style={styles.eventTime}>⏰ First Saturday of each month, 2:00 PM</Text>
                  <Text style={styles.eventLocation}>🌍 Virtual + In-person options</Text>
                  <Text style={styles.eventHost}>👩‍⚕️ Led by SADAG professionals</Text>
                </View>
                <TouchableOpacity style={styles.joinEventButton}>
                  <Text style={styles.joinEventText}>Register Now</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    );
  };

  // Resources Screen
  const ResourcesScreen = () => (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      <View style={styles.screenHeader}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.screenTitle}>Health Resources</Text>
        <View style={{ width: 50 }} />
      </View>

      <ScrollView style={styles.screenContent}>


        <View style={styles.resourcesHeader}>
          <Text style={styles.resourcesWelcome}>Knowledge is power</Text>
          <Text style={styles.resourcesSubtext}>
            Access trusted health information, tools, and emergency resources.
          </Text>
        </View>

        <View style={styles.resourceCategories}>
          <TouchableOpacity
            style={styles.resourceCategory}
            onPress={() => openResourceCategory('Mental Health')}
          >
            <Text style={styles.resourceCategoryIcon}>🧠</Text>
            <Text style={styles.resourceCategoryText}>Mental Health</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.resourceCategory}
            onPress={() => openResourceCategory('Physical Health')}
          >
            <Text style={styles.resourceCategoryIcon}>💪</Text>
            <Text style={styles.resourceCategoryText}>Physical Health</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.resourceCategory}
            onPress={() => openResourceCategory('Nutrition')}
          >
            <Text style={styles.resourceCategoryIcon}>🥗</Text>
            <Text style={styles.resourceCategoryText}>Nutrition</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.resourceCategory}
            onPress={() => openResourceCategory('Health Quizzes')}
          >
            <Text style={styles.resourceCategoryIcon}>🎯</Text>
            <Text style={styles.resourceCategoryText}>Health Quizzes</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.resourceCategory}
            onPress={() => {
              setShowDidYouKnowModal(true);
              addPoints(3);
            }}
          >
            <Text style={styles.resourceCategoryIcon}>💡</Text>
            <Text style={styles.resourceCategoryText}>Did You Know</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.resourceCategory}
            onPress={() => setShowEmergencyModal(true)}
          >
            <Text style={styles.resourceCategoryIcon}>🚨</Text>
            <Text style={styles.resourceCategoryText}>Emergency</Text>
          </TouchableOpacity>
        </View>

        {/* Featured Quiz Section */}
        <View style={styles.featuredSection}>
          <Text style={styles.featuredTitle}>🎯 Today's Health Challenge</Text>
          <TouchableOpacity
            style={styles.featuredQuiz}
            onPress={() => startQuiz(healthQuizzes[0])}
          >
            <View style={styles.quizHeader}>
              <Text style={styles.quizIcon}>🧠</Text>
              <View style={styles.quizInfo}>
                <Text style={styles.quizTitle}>Mental Wellness Quiz</Text>
                <Text style={styles.quizDescription}>Test your mental health knowledge</Text>
                <Text style={styles.quizMeta}>3 questions • 5 min • 50 points</Text>
              </View>
            </View>
            <View style={styles.quizCta}>
              <Text style={styles.quizCtaText}>Start Quiz</Text>
              <Text style={styles.quizCtaIcon}>▶️</Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Available Quizzes Section */}
        <View style={styles.quizzesSection}>
          <Text style={styles.sectionTitle}>🎯 Health Knowledge Quizzes</Text>
          {healthQuizzes.map((quiz) => (
            <TouchableOpacity
              key={quiz.id}
              style={styles.quizCard}
              onPress={() => startQuiz(quiz)}
            >
              <View style={styles.quizCardHeader}>
                <Text style={styles.quizCardIcon}>
                  {quiz.category === 'Mental Health' ? '🧠' :
                   quiz.category === 'Nutrition' ? '🥗' :
                   quiz.category === 'Physical Health' ? '💪' : '🎯'}
                </Text>
                <View style={styles.quizCardInfo}>
                  <Text style={styles.quizCardTitle}>{quiz.title}</Text>
                  <Text style={styles.quizCardDescription}>{quiz.description}</Text>
                  <View style={styles.quizCardMeta}>
                    <Text style={styles.quizCardMetaText}>
                      {quiz.questions.length} questions • {quiz.estimatedTime} min • {quiz.points} points
                    </Text>
                    <Text style={styles.quizDifficulty}>
                      {quiz.difficulty === 'easy' ? '⭐' :
                       quiz.difficulty === 'medium' ? '⭐⭐' : '⭐⭐⭐'}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={styles.quizCardAction}>
                <Text style={styles.quizCardActionText}>Take Quiz →</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.resourcesList}>
          <Text style={styles.sectionTitle}>📚 Health Resources</Text>
          {resources.map((item) => (
            <View key={item.id} style={styles.resourceCard}>
              <View style={styles.resourceHeader}>
                <Text style={styles.resourceIcon}>
                  {item.type === 'article' ? '📄' :
                   item.type === 'video' ? '🎥' :
                   item.type === 'audio' ? '🎧' :
                   item.type === 'quiz' ? '🎯' : '🛠️'}
                </Text>
                <View style={styles.resourceInfo}>
                  <Text style={styles.resourceTitle}>{item.title}</Text>
                  <Text style={styles.resourceDescription}>{item.description}</Text>
                  <Text style={styles.resourceCategory}>
                    {item.category.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.resourceButton}
                onPress={() => openResource(item)}
              >
                <Text style={styles.resourceButtonText}>
                  {item.type === 'tool' ? 'Use Tool' :
                   item.type === 'quiz' ? 'Take Quiz' : 'Read More'}
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );

  // Quiz Screen
  const QuizScreen = () => {
    if (!currentQuiz) return null;

    const currentQuestion = currentQuiz.questions[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / currentQuiz.questions.length) * 100;

    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

        {/* Quiz Header */}
        <View style={styles.quizScreenHeader}>
          <TouchableOpacity onPress={exitQuiz}>
            <Text style={styles.backButton}>✕ Exit</Text>
          </TouchableOpacity>
          <View style={styles.quizProgress}>
            <Text style={styles.quizProgressText}>
              {currentQuestionIndex + 1} of {currentQuiz.questions.length}
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progress}%` }]} />
            </View>
          </View>
          <Text style={styles.quizPoints}>+{currentQuiz.points} pts</Text>
        </View>

        <ScrollView style={styles.quizContent}>
          {/* Quiz Info */}
          <View style={styles.quizInfo}>
            <Text style={styles.quizTitle}>{currentQuiz.title}</Text>
            <Text style={styles.quizCategory}>{currentQuiz.category}</Text>
          </View>

          {/* Question */}
          <View style={styles.questionContainer}>
            <Text style={styles.questionNumber}>Question {currentQuestionIndex + 1}</Text>
            <Text style={styles.questionText}>{currentQuestion.question}</Text>
          </View>

          {/* Answer Options */}
          <View style={styles.answersContainer}>
            {currentQuestion.options.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.answerOption,
                  selectedAnswer === index && styles.selectedAnswer
                ]}
                onPress={() => selectQuizAnswer(index)}
              >
                <View style={styles.answerOptionContent}>
                  <View style={[
                    styles.answerRadio,
                    selectedAnswer === index && styles.selectedRadio
                  ]}>
                    {selectedAnswer === index && <View style={styles.radioInner} />}
                  </View>
                  <Text style={[
                    styles.answerText,
                    selectedAnswer === index && styles.selectedAnswerText
                  ]}>
                    {option}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[
              styles.submitAnswerButton,
              selectedAnswer === null && styles.submitAnswerButtonDisabled
            ]}
            onPress={submitQuizAnswer}
            disabled={selectedAnswer === null}
          >
            <Text style={styles.submitAnswerButtonText}>
              {currentQuestionIndex < currentQuiz.questions.length - 1 ? 'Next Question' : 'Finish Quiz'}
            </Text>
          </TouchableOpacity>

          {/* Quiz Meta Info */}
          <View style={styles.quizMeta}>
            <Text style={styles.quizMetaText}>
              Difficulty: {currentQuiz.difficulty.charAt(0).toUpperCase() + currentQuiz.difficulty.slice(1)}
            </Text>
            <Text style={styles.quizMetaText}>
              Estimated time: {currentQuiz.estimatedTime} minutes
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  };

  // Emergency Modal
  const EmergencyModal = () => (
    <Modal
      visible={showEmergencyModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.emergencyModalContainer}>
        <View style={styles.emergencyHeader}>
          <Text style={styles.emergencyTitle}>🚨 Crisis Support</Text>
          <TouchableOpacity onPress={() => setShowEmergencyModal(false)}>
            <Text style={styles.emergencyClose}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.emergencyContent}>
          <View style={styles.emergencySection}>
            <Text style={styles.emergencyMessage}>
              If you're experiencing a mental health crisis or having thoughts of self-harm,
              please reach out for immediate help. You are not alone.
            </Text>
          </View>

          <View style={styles.emergencyContacts}>
            <Text style={styles.emergencyContactsTitle}>South Africa Emergency Contacts</Text>

            <TouchableOpacity
              style={styles.emergencyContact}
              onPress={() => callEmergencyNumber('0800 567 567', 'SADAG Crisis Line')}
            >
              <Text style={styles.emergencyContactIcon}>📞</Text>
              <View style={styles.emergencyContactInfo}>
                <Text style={styles.emergencyContactName}>Crisis Helpline</Text>
                <Text style={styles.emergencyContactNumber}>0800 567 567</Text>
                <Text style={styles.emergencyContactDesc}>24/7 Crisis support</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.emergencyContact}
              onPress={() => callEmergencyNumber('10177', 'Emergency Services')}
            >
              <Text style={styles.emergencyContactIcon}>🆘</Text>
              <View style={styles.emergencyContactInfo}>
                <Text style={styles.emergencyContactName}>Suicide Prevention</Text>
                <Text style={styles.emergencyContactNumber}>0800 121 314</Text>
                <Text style={styles.emergencyContactDesc}>Suicide prevention hotline</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity style={styles.emergencyContact}>
              <Text style={styles.emergencyContactIcon}>🚑</Text>
              <View style={styles.emergencyContactInfo}>
                <Text style={styles.emergencyContactName}>Emergency Services</Text>
                <Text style={styles.emergencyContactNumber}>10177</Text>
                <Text style={styles.emergencyContactDesc}>Medical emergencies</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.emergencyContact}
              onPress={() => callEmergencyNumber('31393', 'SMS Counseling')}
            >
              <Text style={styles.emergencyContactIcon}>💬</Text>
              <View style={styles.emergencyContactInfo}>
                <Text style={styles.emergencyContactName}>SMS Counseling</Text>
                <Text style={styles.emergencyContactNumber}>31393</Text>
                <Text style={styles.emergencyContactDesc}>Text-based support</Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.emergencySection}>
            <Text style={styles.emergencyNote}>
              Remember: Seeking help is a sign of strength, not weakness.
              Professional support can make a real difference.
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  // Did You Know Modal
  const DidYouKnowModal = () => (
    <Modal
      visible={showDidYouKnowModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowDidYouKnowModal(false)}>
            <Text style={styles.modalCancel}>Close</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>💡 Did You Know?</Text>
          <TouchableOpacity onPress={() => {
            addPoints(5);
            setShowDidYouKnowModal(false);
          }}>
            <Text style={styles.modalSave}>+5 ⭐</Text>
          </TouchableOpacity>
        </View>

        {dailyFact && (
          <ScrollView style={styles.modalContent}>
            <View style={styles.factCard}>
              <View style={styles.factHeader}>
                <Text style={styles.factEmoji}>{dailyFact.emoji}</Text>
                <View style={styles.factMeta}>
                  <Text style={styles.factCategory}>{dailyFact.category.replace('_', ' ').toUpperCase()}</Text>
                  <Text style={styles.funLevel}>
                    Fun Level: {'⭐'.repeat(dailyFact.funLevel)}
                  </Text>
                </View>
              </View>

              <Text style={styles.factText}>{dailyFact.fact}</Text>

              {dailyFact.actionTip && (
                <View style={styles.actionTipContainer}>
                  <Text style={styles.actionTipTitle}>💪 Try This Today:</Text>
                  <Text style={styles.actionTipText}>{dailyFact.actionTip}</Text>
                </View>
              )}

              <Text style={styles.factSource}>Source: {dailyFact.source}</Text>
            </View>

            <TouchableOpacity
              style={styles.moreFactsButton}
              onPress={() => {
                setShowDidYouKnowModal(false);
                setCurrentScreen('resources');
              }}
            >
              <Text style={styles.moreFactsButtonText}>🧠 Explore More Health Facts</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.shareFactButton}
              onPress={() => {
                // Share functionality would go here
                Alert.alert('Share', 'Share this amazing fact with friends!');
              }}
            >
              <Text style={styles.shareFactButtonText}>📤 Share This Fact</Text>
            </TouchableOpacity>
          </ScrollView>
        )}
      </SafeAreaView>
    </Modal>
  );

  // Enhanced Chat Screen - Memoized to prevent re-renders
  const ChatScreen = React.memo(() => (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Enhanced Chat Header */}
      <View style={styles.chatHeader}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')} style={styles.backButtonContainer}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <View style={styles.chatHeaderCenter}>
          <Text style={styles.chatTitle}>AI Health Assistant</Text>
          <Text style={styles.chatSubtitle}>
            {isTyping ? 'AI is typing...' : 'Online • Secure & Private'}
          </Text>
        </View>
        <TouchableOpacity onPress={createNewChatSession} style={styles.newChatButton}>
          <Text style={styles.newChatIcon}>+</Text>
        </TouchableOpacity>
      </View>



      {/* Messages Container with Enhanced UI */}
      <KeyboardAvoidingView
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <ScrollView
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
        {messages.map((message) => (
          <View
            key={message.id}
            style={[
              styles.messageContainer,
              message.sender === 'user' ? styles.userMessage : styles.aiMessage
            ]}
          >
            {/* Message Content */}
            <View style={[
              styles.messageBubble,
              message.sender === 'user' ? styles.userMessageBubble : styles.aiMessageBubble,
              message.isError && styles.errorMessageBubble
            ]}>
              <Text style={[
                styles.messageText,
                message.sender === 'user' ? styles.userMessageText : styles.aiMessageText,
                message.isError && styles.errorMessageText
              ]}>
                {message.text}
              </Text>

              {/* Message Metadata */}
              <View style={styles.messageMetadata}>
                <Text style={[
                  styles.messageTime,
                  message.sender === 'user' ? styles.userMessageTime : styles.aiMessageTime
                ]}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>

                {/* Delivery Status for User Messages */}
                {message.sender === 'user' && (
                  <Text style={styles.deliveryStatus}>
                    {messageDeliveryStatus[message.id] === 'sending' ? '⏳' :
                     messageDeliveryStatus[message.id] === 'delivered' ? '✓' :
                     messageDeliveryStatus[message.id] === 'failed' ? '❌' : '✓'}
                  </Text>
                )}

                {/* Confidence Score for AI Messages */}
                {message.sender === 'ai' && message.confidence && (
                  <Text style={styles.confidenceScore}>
                    {Math.round(message.confidence * 100)}% confident
                  </Text>
                )}
              </View>

              {/* Emergency Indicator */}
              {message.isEmergency && (
                <View style={styles.emergencyIndicatorContainer}>
                  <Text style={styles.emergencyIndicator}>🚨 Emergency Detected</Text>
                  <TouchableOpacity
                    style={styles.emergencyButton}
                    onPress={() => setShowEmergencyModal(true)}
                  >
                    <Text style={styles.emergencyButtonText}>Get Help Now</Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Medical Topics Tags */}
              {message.medicalTopics && message.medicalTopics.length > 0 && (
                <View style={styles.topicsContainer}>
                  {message.medicalTopics.slice(0, 3).map((topic, index) => (
                    <View key={index} style={styles.topicTag}>
                      <Text style={styles.topicText}>{topic}</Text>
                    </View>
                  ))}
                </View>
              )}

              {/* Recommendations */}
              {message.recommendations && message.recommendations.length > 0 && (
                <View style={styles.recommendationsContainer}>
                  <Text style={styles.recommendationsTitle}>💡 Recommendations:</Text>
                  {message.recommendations.slice(0, 2).map((rec, index) => (
                    <Text key={index} style={styles.recommendationText}>• {rec}</Text>
                  ))}
                </View>
              )}
            </View>
          </View>
        ))}

        {/* Enhanced Typing Indicator */}
        {isTyping && (
          <View style={[styles.messageContainer, styles.aiMessage]}>
            <View style={[styles.messageBubble, styles.aiMessageBubble, styles.typingBubble]}>
              <View style={styles.typingIndicator}>
                <View style={[styles.typingDot, styles.typingDot1]} />
                <View style={[styles.typingDot, styles.typingDot2]} />
                <View style={[styles.typingDot, styles.typingDot3]} />
              </View>
              <Text style={styles.typingText}>AI is analyzing your message...</Text>
            </View>
          </View>
        )}

        {/* Loading Indicator */}
        {isLoading && !isTyping && (
          <View style={[styles.messageContainer, styles.aiMessage]}>
            <View style={[styles.messageBubble, styles.aiMessageBubble]}>
              <Text style={styles.aiMessageText}>Processing your request...</Text>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Enhanced Input Container */}
      <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>
          <TextInput
            ref={textInputRef}
            style={styles.textInput}
            value={inputText}
            onChangeText={handleInputChange}
            placeholder="Describe your symptoms or ask a health question..."
            multiline
            maxLength={500}
            placeholderTextColor="#9ca3af"
            autoCorrect={true}
            autoCapitalize="sentences"
            returnKeyType="send"
            blurOnSubmit={false}
            textAlignVertical="top"
            keyboardType="default"
            onSubmitEditing={() => {
              if (inputText.trim()) {
                const userMessage = {
                  id: Date.now().toString(),
                  text: inputText.trim(),
                  sender: 'user' as const,
                  timestamp: new Date(),
                };
                setMessages(prev => [...prev, userMessage]);
                setInputText('');
              }
            }}
          />
          <Text style={styles.characterCount}>{inputText.length}/500</Text>
        </View>

        <TouchableOpacity
          style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
          onPress={() => {
            if (inputText.trim()) {
              const userMessage = {
                id: Date.now().toString(),
                text: inputText.trim(),
                sender: 'user' as const,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);
              setInputText('');
            }
          }}
          disabled={!inputText.trim()}
        >
          <Text style={styles.sendButtonText}>
            {isLoading ? '⏳' : '➤'}
          </Text>
        </TouchableOpacity>
      </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => setInputText("I'm feeling anxious")}
          >
            <Text style={styles.quickActionText}>😰 Anxiety</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => setInputText("I have a headache")}
          >
            <Text style={styles.quickActionText}>🤕 Headache</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => setInputText("I need emergency help")}
          >
            <Text style={styles.quickActionText}>🚨 Emergency</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  ));

  // Simple Profile Screen
  const ProfileScreen = () => (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.screenTitle}>Profile</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.homeContainer}>
        <View style={styles.profileCard}>
          <View style={styles.profileAvatar}>
            <Text style={styles.profileAvatarText}>👤</Text>
          </View>
          <Text style={styles.profileName}>Health App User</Text>
          <Text style={styles.profileEmail}><EMAIL></Text>
        </View>

        <View style={styles.profileStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{userPoints}</Text>
            <Text style={styles.statLabel}>Points Earned</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{currentStreak}</Text>
            <Text style={styles.statLabel}>Day Streak</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{journalEntries.length}</Text>
            <Text style={styles.statLabel}>Journal Entries</Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.profileOption}
          onPress={() => Alert.alert('Settings', 'App settings would be displayed here')}
        >
          <Text style={styles.profileOptionIcon}>⚙️</Text>
          <Text style={styles.profileOptionText}>Settings</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.profileOption}
          onPress={() => Alert.alert('Privacy', 'Privacy policy and data settings')}
        >
          <Text style={styles.profileOptionIcon}>🔒</Text>
          <Text style={styles.profileOptionText}>Privacy & Security</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.profileOption}
          onPress={() => Alert.alert('Help', 'Help and support information')}
        >
          <Text style={styles.profileOptionIcon}>❓</Text>
          <Text style={styles.profileOptionText}>Help & Support</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );

  // Enhanced Appointments Screen
  const AppointmentsScreen = () => {
    const filteredAppointments = appointments.filter(appointment => {
      const now = new Date();
      const appointmentDate = new Date(appointment.date);

      switch (appointmentFilter) {
        case 'upcoming':
          return appointmentDate >= now && appointment.status !== 'cancelled';
        case 'past':
          return appointmentDate < now || appointment.status === 'completed';
        case 'cancelled':
          return appointment.status === 'cancelled';
        default:
          return true;
      }
    });



    const getPriorityColor = (priority?: string) => {
      switch (priority) {
        case 'emergency': return '#EF4444';
        case 'urgent': return '#F59E0B';
        default: return '#22C55E';
      }
    };

    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#F0FDF4" />

        {/* Header */}
        <View style={styles.appointmentsHeader}>
          <TouchableOpacity onPress={() => setCurrentScreen('home')} style={styles.backButtonContainer}>
            <Text style={styles.backButton}>← Back</Text>
          </TouchableOpacity>
          <View style={styles.appointmentsHeaderCenter}>
            <Text style={styles.screenTitle}>Appointments</Text>
            <Text style={styles.appointmentsSubtitle}>{filteredAppointments.length} {appointmentFilter} appointments</Text>
          </View>
          <TouchableOpacity
            style={styles.addAppointmentButton}
            onPress={() => setShowAppointmentModal(true)}
          >
            <Text style={styles.addAppointmentIcon}>+</Text>
          </TouchableOpacity>
        </View>

        {/* Filter Tabs */}
        <View style={styles.appointmentFilters}>
          {['upcoming', 'past', 'cancelled', 'all'].map((filter) => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterTab,
                appointmentFilter === filter && styles.filterTabActive
              ]}
              onPress={() => setAppointmentFilter(filter as any)}
            >
              <Text style={[
                styles.filterTabText,
                appointmentFilter === filter && styles.filterTabTextActive
              ]}>
                {filter.charAt(0).toUpperCase() + filter.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <ScrollView style={styles.screenContent} showsVerticalScrollIndicator={false}>
          {filteredAppointments.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyIcon}>📅</Text>
              <Text style={styles.emptyTitle}>No {appointmentFilter} appointments</Text>
              <Text style={styles.emptyText}>
                {appointmentFilter === 'upcoming'
                  ? 'Book your first appointment to get started with your health journey.'
                  : `You don't have any ${appointmentFilter} appointments.`}
              </Text>
              <TouchableOpacity
                style={styles.emptyActionButton}
                onPress={() => setShowAppointmentModal(true)}
              >
                <Text style={styles.emptyActionText}>Book Appointment</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.appointmentsContainer}>
              {filteredAppointments.map((appointment) => (
                <TouchableOpacity
                  key={appointment.id}
                  style={styles.appointmentCard}
                  onPress={() => setSelectedAppointment(appointment)}
                >
                  {/* Appointment Header */}
                  <View style={styles.appointmentCardHeader}>
                    <View style={styles.appointmentTitleContainer}>
                      <Text style={styles.appointmentTitle}>{appointment.title}</Text>
                      <View style={styles.appointmentMeta}>
                        <Text style={styles.appointmentProvider}>{appointment.provider}</Text>
                        <Text style={styles.appointmentType}>• {appointment.type.replace('_', ' ')}</Text>
                      </View>
                    </View>
                    <View style={styles.appointmentStatusContainer}>
                      <View style={[styles.statusBadge, { backgroundColor: getStatusColor(appointment.status) }]}>
                        <Text style={styles.statusIcon}>{getStatusIcon(appointment.status)}</Text>
                        <Text style={styles.statusText}>{appointment.status.replace('_', ' ')}</Text>
                      </View>
                      {appointment.priority && appointment.priority !== 'normal' && (
                        <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(appointment.priority) }]}>
                          <Text style={styles.priorityText}>{appointment.priority.toUpperCase()}</Text>
                        </View>
                      )}
                    </View>
                  </View>

                  {/* Appointment Details */}
                  <View style={styles.appointmentDetails}>
                    <View style={styles.appointmentDetailRow}>
                      <Text style={styles.appointmentDetailIcon}>📅</Text>
                      <Text style={styles.appointmentDetailText}>
                        {formatDate(appointment.date)} at {appointment.time}
                      </Text>
                    </View>
                    {appointment.location && (
                      <View style={styles.appointmentDetailRow}>
                        <Text style={styles.appointmentDetailIcon}>📍</Text>
                        <Text style={styles.appointmentDetailText}>{appointment.location}</Text>
                      </View>
                    )}
                    {appointment.queuePosition && (
                      <View style={styles.appointmentDetailRow}>
                        <Text style={styles.appointmentDetailIcon}>👥</Text>
                        <Text style={styles.appointmentDetailText}>
                          Queue position: #{appointment.queuePosition}
                          {appointment.estimatedWaitTime && ` • ~${appointment.estimatedWaitTime} min wait`}
                        </Text>
                      </View>
                    )}
                  </View>

                  {/* Quick Actions */}
                  <View style={styles.appointmentActions}>
                    {appointment.status === 'confirmed' && (
                      <TouchableOpacity
                        style={styles.quickActionButton}
                        onPress={() => checkInAppointment(appointment.id)}
                      >
                        <Text style={styles.quickActionText}>📱 Check In</Text>
                      </TouchableOpacity>
                    )}
                    {appointment.status === 'scheduled' && (
                      <TouchableOpacity
                        style={styles.quickActionButton}
                        onPress={() => confirmAppointment(appointment.id)}
                      >
                        <Text style={styles.quickActionText}>✅ Confirm</Text>
                      </TouchableOpacity>
                    )}
                    <TouchableOpacity
                      style={styles.quickActionButton}
                      onPress={() => {
                        setSelectedAppointment(appointment);
                        setShowAppointmentDetails(true);
                      }}
                    >
                      <Text style={styles.quickActionText}>📝 Details</Text>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    );
  };

  // Appointment Details Modal
  const AppointmentDetailsModal = () => {
    if (!selectedAppointment) return null;

    return (
      <Modal
        visible={showAppointmentDetails}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAppointmentDetails(false)}>
              <Text style={styles.modalCloseButton}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Appointment Details</Text>
            <View style={styles.modalHeaderSpacer} />
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Appointment Header */}
            <View style={styles.appointmentDetailHeader}>
              <Text style={styles.appointmentDetailTitle}>{selectedAppointment.title}</Text>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedAppointment.status) }]}>
                <Text style={styles.statusIcon}>{getStatusIcon(selectedAppointment.status)}</Text>
                <Text style={styles.statusText}>{selectedAppointment.status.replace('_', ' ')}</Text>
              </View>
            </View>

            {/* Appointment Info */}
            <View style={styles.appointmentDetailSection}>
              <Text style={styles.sectionTitle}>📅 Appointment Information</Text>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Date & Time:</Text>
                <Text style={styles.detailValue}>{formatDate(selectedAppointment.date)} at {selectedAppointment.time}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Provider:</Text>
                <Text style={styles.detailValue}>{selectedAppointment.provider}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Type:</Text>
                <Text style={styles.detailValue}>{selectedAppointment.type.replace('_', ' ')}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Location:</Text>
                <Text style={styles.detailValue}>{selectedAppointment.location}</Text>
              </View>
              {selectedAppointment.cost && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Cost:</Text>
                  <Text style={styles.detailValue}>R{selectedAppointment.cost}</Text>
                </View>
              )}
            </View>

            {/* Queue Information */}
            {selectedAppointment.queuePosition && (
              <View style={styles.appointmentDetailSection}>
                <Text style={styles.sectionTitle}>👥 Queue Information</Text>
                <View style={styles.queueInfoCard}>
                  <View style={styles.queueInfoItem}>
                    <Text style={styles.queueInfoLabel}>Your Position</Text>
                    <Text style={styles.queueInfoValue}>#{selectedAppointment.queuePosition}</Text>
                  </View>
                  <View style={styles.queueInfoItem}>
                    <Text style={styles.queueInfoLabel}>Estimated Wait</Text>
                    <Text style={styles.queueInfoValue}>{selectedAppointment.estimatedWaitTime} min</Text>
                  </View>
                </View>
              </View>
            )}

            {/* Preparation Checklist */}
            {selectedAppointment.preparationChecklist && (
              <View style={styles.appointmentDetailSection}>
                <Text style={styles.sectionTitle}>📋 Preparation Checklist</Text>
                {selectedAppointment.preparationChecklist.map((item, index) => (
                  <View key={index} style={styles.checklistItem}>
                    <Text style={styles.checklistIcon}>✓</Text>
                    <Text style={styles.checklistText}>{item}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Notes */}
            {selectedAppointment.notes && (
              <View style={styles.appointmentDetailSection}>
                <Text style={styles.sectionTitle}>📝 Notes</Text>
                <Text style={styles.notesText}>{selectedAppointment.notes}</Text>
              </View>
            )}

            {/* Action Buttons */}
            <View style={styles.appointmentDetailActions}>
              {selectedAppointment.status === 'scheduled' && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.confirmButton]}
                  onPress={() => {
                    confirmAppointment(selectedAppointment.id);
                    setShowAppointmentDetails(false);
                  }}
                >
                  <Text style={styles.actionButtonText}>✅ Confirm Appointment</Text>
                </TouchableOpacity>
              )}

              {selectedAppointment.status === 'confirmed' && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.checkInButton]}
                  onPress={() => {
                    checkInAppointment(selectedAppointment.id);
                    setShowAppointmentDetails(false);
                  }}
                >
                  <Text style={styles.actionButtonText}>📱 Check In Now</Text>
                </TouchableOpacity>
              )}

              {(selectedAppointment.status === 'scheduled' || selectedAppointment.status === 'confirmed') && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.rescheduleButton]}
                  onPress={() => {
                    setShowAppointmentDetails(false);
                    setShowRescheduleModal(true);
                  }}
                >
                  <Text style={styles.actionButtonText}>📅 Reschedule</Text>
                </TouchableOpacity>
              )}

              {(selectedAppointment.status === 'scheduled' || selectedAppointment.status === 'confirmed') && (
                <TouchableOpacity
                  style={[styles.actionButton, styles.cancelButton]}
                  onPress={() => {
                    setShowAppointmentDetails(false);
                    cancelAppointment(selectedAppointment.id);
                  }}
                >
                  <Text style={styles.actionButtonText}>❌ Cancel Appointment</Text>
                </TouchableOpacity>
              )}
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    );
  };

  // Appointment Booking Modal
  const AppointmentBookingModal = () => {
    return (
      <Modal
        visible={showAppointmentModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAppointmentModal(false)}>
              <Text style={styles.modalCloseButton}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Book New Appointment</Text>
            <View style={styles.modalHeaderSpacer} />
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Appointment Type */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>Appointment Type</Text>
              <View style={styles.typeSelector}>
                {[
                  { id: 'doctor', name: 'General Doctor', icon: '👨‍⚕️' },
                  { id: 'therapist', name: 'Mental Health', icon: '🧠' },
                  { id: 'specialist', name: 'Specialist', icon: '🔬' },
                  { id: 'mobile_clinic', name: 'Mobile Clinic', icon: '🚐' }
                ].map((type) => (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.typeOption,
                      newAppointmentData.type === type.id && styles.typeOptionSelected
                    ]}
                    onPress={() => setNewAppointmentData(prev => ({ ...prev, type: type.id }))}
                  >
                    <Text style={styles.typeIcon}>{type.icon}</Text>
                    <Text style={[
                      styles.typeText,
                      newAppointmentData.type === type.id && styles.typeTextSelected
                    ]}>
                      {type.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Title */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>Appointment Title</Text>
              <TextInput
                style={styles.formInput}
                value={newAppointmentData.title}
                onChangeText={(text) => setNewAppointmentData(prev => ({ ...prev, title: text }))}
                placeholder="e.g., General Health Checkup"
                placeholderTextColor="#9CA3AF"
                autoCorrect={true}
                autoCapitalize="words"
                returnKeyType="next"
                blurOnSubmit={false}
              />
            </View>

            {/* Provider Selection */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>Select Provider</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.providerScroll}>
                {availableProviders.map((provider) => (
                  <TouchableOpacity
                    key={provider.id}
                    style={[
                      styles.providerCard,
                      newAppointmentData.provider === provider.name && styles.providerCardSelected
                    ]}
                    onPress={() => setNewAppointmentData(prev => ({
                      ...prev,
                      provider: provider.name,
                      location: provider.location
                    }))}
                  >
                    <Text style={styles.providerName}>{provider.name}</Text>
                    <Text style={styles.providerSpecialty}>{provider.specialty}</Text>
                    <Text style={styles.providerRating}>⭐ {provider.rating}</Text>
                    <Text style={styles.providerLocation}>{provider.location}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Date Selection */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>Select Date</Text>
              <TextInput
                style={styles.formInput}
                value={newAppointmentData.date}
                onChangeText={(text) => setNewAppointmentData(prev => ({ ...prev, date: text }))}
                placeholder="YYYY-MM-DD"
                placeholderTextColor="#9CA3AF"
                keyboardType="numeric"
                returnKeyType="next"
                maxLength={10}
                blurOnSubmit={false}
              />
            </View>

            {/* Time Selection */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>Select Time</Text>
              <View style={styles.timeSlotGrid}>
                {availableTimeSlots.map((time) => (
                  <TouchableOpacity
                    key={time}
                    style={[
                      styles.timeSlot,
                      newAppointmentData.time === time && styles.timeSlotSelected
                    ]}
                    onPress={() => setNewAppointmentData(prev => ({ ...prev, time }))}
                  >
                    <Text style={[
                      styles.timeSlotText,
                      newAppointmentData.time === time && styles.timeSlotTextSelected
                    ]}>
                      {time}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Notes */}
            <View style={styles.formSection}>
              <Text style={styles.formLabel}>Additional Notes (Optional)</Text>
              <TextInput
                style={[styles.formInput, styles.formTextArea]}
                value={newAppointmentData.notes}
                onChangeText={(text) => setNewAppointmentData(prev => ({ ...prev, notes: text }))}
                placeholder="Any specific concerns or symptoms..."
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
                maxLength={300}
                autoCorrect={true}
                autoCapitalize="sentences"
                returnKeyType="default"
                blurOnSubmit={false}
              />
            </View>

            {/* Book Button */}
            <TouchableOpacity
              style={styles.bookButton}
              onPress={() => {
                if (!newAppointmentData.title || !newAppointmentData.date || !newAppointmentData.time || !newAppointmentData.provider) {
                  Alert.alert('Missing Information', 'Please fill in all required fields.');
                  return;
                }

                const newAppointment: Appointment = {
                  id: Date.now().toString(),
                  title: newAppointmentData.title,
                  date: new Date(newAppointmentData.date),
                  time: newAppointmentData.time,
                  type: newAppointmentData.type as any,
                  provider: newAppointmentData.provider,
                  location: newAppointmentData.location || 'TBD',
                  notes: newAppointmentData.notes,
                  status: 'scheduled',
                  priority: 'normal',
                  queuePosition: Math.floor(Math.random() * 5) + 1,
                  estimatedWaitTime: Math.floor(Math.random() * 30) + 15,
                  remindersSent: false,
                  preparationChecklist: ['Bring ID document', 'Bring medical aid card'],
                  followUpRequired: false,
                  cost: Math.floor(Math.random() * 500) + 300,
                  insurance: 'Discovery Health'
                };

                setAppointments(prev => [...prev, newAppointment]);
                setNewAppointmentData({
                  title: '',
                  type: 'doctor',
                  date: '',
                  time: '',
                  provider: '',
                  location: '',
                  notes: ''
                });
                setShowAppointmentModal(false);

                Alert.alert(
                  'Appointment Booked! 🎉',
                  `Your appointment with ${newAppointment.provider} has been scheduled for ${formatDate(newAppointment.date)} at ${newAppointment.time}.`,
                  [{ text: 'OK', onPress: () => {} }]
                );
              }}
            >
              <Text style={styles.bookButtonText}>📅 Book Appointment</Text>
            </TouchableOpacity>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    );
  };

  // Render current screen
  const renderScreen = () => {
    switch (currentScreen) {
      case 'splash':
        return <SplashScreen />;
      case 'welcome':
        return <WelcomeScreen />;
      case 'home':
        return <HomeScreen />;
      case 'chat':
        return <ChatScreen />;
      case 'journal':
        return <JournalScreen />;
      case 'appointments':
        return <AppointmentsScreen />;
      case 'community':
        return <CommunityScreen />;
      case 'resources':
        return <ResourcesScreen />;
      case 'quiz':
        return <QuizScreen />;
      case 'profile':
        return <ProfileScreen />;
      default:
        return <WelcomeScreen />;
    }
  };

  return (
    <View style={styles.appContainer}>
      {renderScreen()}

      {/* Appointment Modals */}
      <AppointmentDetailsModal />
      <AppointmentBookingModal />

      {/* Journal Modal */}
      <NewJournalEntryModal />

      <EmergencyModal />
      <DidYouKnowModal />
    </View>
  );
};

const styles = StyleSheet.create({
  // Health-focused Color Palette:
  // Primary: #22C55E (Vibrant Green - Health, Growth, Vitality)
  // Secondary: #059669 (Deep Green - Trust, Healing)
  // Accent: #10B981 (Emerald - Wellness, Balance)
  // Warning: #F59E0B (Amber - Caution, Energy)
  // Error: #EF4444 (Red - Emergency, Alert)
  // Background: #F0FDF4 (Light Green - Calm, Fresh)
  // Surface: #FFFFFF (White - Clean, Pure)
  // Text Primary: #1F2937 (Dark Gray - Readable)
  // Text Secondary: #6B7280 (Medium Gray - Subtle)

  // Splash Screen Styles
  splashContainer: {
    flex: 1,
    backgroundColor: '#22C55E',
    justifyContent: 'center',
    alignItems: 'center',
  },
  splashContent: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  splashLogoContainer: {
    alignItems: 'center',
    marginTop: 60,
  },
  splashLogo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  splashLogoText: {
    fontSize: 60,
  },
  splashTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  splashSubtitle: {
    fontSize: 18,
    color: '#ffffff',
    opacity: 0.9,
    textAlign: 'center',
  },
  splashMessageContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  splashWelcomeText: {
    fontSize: 20,
    color: '#ffffff',
    opacity: 0.8,
    marginBottom: 8,
    textAlign: 'center',
  },
  splashMainMessage: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 16,
    textAlign: 'center',
  },
  splashDescription: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
    textAlign: 'center',
    lineHeight: 24,
  },
  splashFooter: {
    alignItems: 'center',
  },
  loadingIndicator: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  loadingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ffffff',
    marginHorizontal: 4,
    opacity: 0.6,
  },
  loadingDotDelay1: {
    opacity: 0.8,
  },
  loadingDotDelay2: {
    opacity: 1,
  },
  splashLoadingText: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.7,
  },

  // Welcome Screen Styles
  welcomeScreenContainer: {
    flex: 1,
    backgroundColor: '#F0FDF4',
  },
  welcomeHeader: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 40,
  },
  welcomeLogo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#22C55E',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  welcomeLogoText: {
    fontSize: 40,
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 18,
    color: '#059669',
    marginBottom: 16,
    textAlign: 'center',
    fontWeight: '600',
  },
  welcomeDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  welcomeConfigSection: {
    flex: 1,
  },
  welcomeSection: {
    marginBottom: 32,
  },
  welcomeSectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  welcomeSectionSubtitle: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 20,
  },
  welcomeOptionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  welcomeOptionButton: {
    flex: 1,
    minWidth: '45%',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#e9ecef',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  welcomeOptionButtonSelected: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  welcomeOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  welcomeOptionTextSelected: {
    color: '#059669',
  },
  welcomeModeButton: {
    flex: 1,
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#e9ecef',
    alignItems: 'center',
    marginHorizontal: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  welcomeModeButtonSelected: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  welcomeModeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 4,
  },
  welcomeModeTextSelected: {
    color: '#059669',
  },
  welcomeModeDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  welcomeModeDescriptionSelected: {
    color: '#059669',
  },
  welcomeActionSection: {
    paddingTop: 20,
    paddingBottom: 40,
  },
  welcomeContinueButton: {
    flexDirection: 'row',
    backgroundColor: '#22C55E',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  welcomeContinueButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginRight: 8,
  },
  welcomeContinueButtonIcon: {
    fontSize: 18,
    color: '#ffffff',
  },
  welcomePrivacySection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  welcomePrivacyIcon: {
    fontSize: 20,
    marginRight: 12,
    marginTop: 2,
  },
  welcomePrivacyText: {
    flex: 1,
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 20,
  },

  // Original Styles
  appContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  container: {
    flex: 1,
    backgroundColor: '#F0FDF4',
  },
  welcomeContainer: {
    flexGrow: 1,
    padding: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 28,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 16,
  },
  logoText: {
    fontSize: 42,
    color: 'white',
  },
  title: {
    fontSize: 34,
    fontWeight: '800',
    color: '#1a202c',
    marginBottom: 12,
    letterSpacing: -1,
  },
  subtitle: {
    fontSize: 18,
    color: '#4a5568',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    flex: 1,
    minWidth: '45%',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d5db',
    backgroundColor: '#f9fafb',
    alignItems: 'center',
  },
  optionButtonSelected: {
    borderColor: '#2563eb',
    backgroundColor: '#eff6ff',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  optionTextSelected: {
    color: '#2563eb',
  },
  continueButton: {
    backgroundColor: '#2563eb',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  continueButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  privacyText: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 16,
  },
  homeContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 100, // Space for bottom nav
    backgroundColor: '#F0FDF4',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 4,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  profileButtonText: {
    fontSize: 20,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  pointsContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  pointsText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#059669',
  },
  // Streak Banner
  streakBanner: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  streakInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  streakIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  streakNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#F59E0B',
  },
  streakLabel: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  achievementPreview: {
    flexDirection: 'row',
  },
  achievementBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  achievementIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  achievementCount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  // Profile Styles
  profileCard: {
    backgroundColor: '#F0FDF4',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#D1FAE5',
  },
  profileAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#22C55E',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileAvatarText: {
    fontSize: 32,
    color: 'white',
  },
  profileName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#6b7280',
  },
  profileStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#059669',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  profileOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  profileOptionIcon: {
    fontSize: 20,
    marginRight: 16,
  },
  profileOptionText: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
  },
  // Did You Know Card
  didYouKnowCard: {
    backgroundColor: '#eff6ff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 2,
    borderColor: '#2563eb',
    shadowColor: '#2563eb',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  didYouKnowHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  didYouKnowTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2563eb',
  },
  funLevelIndicator: {
    fontSize: 12,
  },
  didYouKnowFact: {
    fontSize: 16,
    color: '#1f2937',
    lineHeight: 24,
    marginBottom: 12,
  },
  didYouKnowCta: {
    fontSize: 14,
    color: '#2563eb',
    fontWeight: '500',
    textAlign: 'center',
  },
  smallLogo: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  smallLogoText: {
    color: 'white',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  questionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  // Card Styles
  quickCheckCard: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 20,
    marginBottom: 28,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: '#f1f5f9',
    overflow: 'hidden',
  },
  cardTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 20,
    letterSpacing: -0.5,
  },
  // Mood Selector
  moodSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingHorizontal: 4,
    gap: 4,
  },
  moodButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 16,
    flex: 1,
    marginHorizontal: 2,
    backgroundColor: '#F0FDF4',
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    maxWidth: 70,
    minHeight: 80,
    overflow: 'hidden',
  },
  moodButtonSelected: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
    shadowColor: '#22C55E',
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    transform: [{ scale: 1.02 }],
  },
  moodEmoji: {
    fontSize: 20,
    marginBottom: 2,
    textAlign: 'center',
    lineHeight: 24,
  },
  moodLabel: {
    fontSize: 10,
    fontWeight: '600',
    color: '#4a5568',
    textAlign: 'center',
    marginTop: 2,
    numberOfLines: 1,
    flexShrink: 1,
  },
  moodLabelSelected: {
    color: '#ffffff',
    fontWeight: '700',
  },

  // Quick Access Bar
  quickAccessBar: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 20,
    marginHorizontal: 4,
    marginBottom: 24,
    paddingVertical: 16,
    paddingHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  quickAccessButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 16,
    marginHorizontal: 4,
  },
  primaryQuickAccess: {
    backgroundColor: '#22C55E',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
  },
  emergencyQuickAccess: {
    backgroundColor: '#EF4444',
    shadowColor: '#EF4444',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
  },
  quickAccessIcon: {
    fontSize: 24,
    marginBottom: 6,
  },
  quickAccessLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4a5568',
    textAlign: 'center',
  },
  quickAccessLabelActive: {
    color: '#ffffff',
    fontWeight: '700',
  },



  // Action Grid
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionCard: {
    width: '47%',
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 24,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  primaryCard: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
    shadowColor: '#22C55E',
    shadowOpacity: 0.25,
  },
  emergencyCard: {
    backgroundColor: '#EF4444',
    borderColor: '#EF4444',
    shadowColor: '#EF4444',
    shadowOpacity: 0.25,
  },
  actionIcon: {
    fontSize: 36,
    marginBottom: 16,
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a202c',
    textAlign: 'center',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  actionSubtitle: {
    fontSize: 14,
    color: '#4a5568',
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 18,
  },
  emergencyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 4,
  },
  emergencySubtitle: {
    fontSize: 12,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
  },
  // Recent Activity Card
  recentCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 120, // Increased margin to move card up from quick access bar
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  recentItem: {
    marginTop: 8,
  },
  recentText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  recentMood: {
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '500',
  },
  // Bottom Navigation
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 20,
    right: 20,
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 24,
    paddingVertical: 16,
    paddingHorizontal: 8,
    marginBottom: Platform.OS === 'ios' ? 34 : 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 16,
  },
  navItemActive: {
    backgroundColor: '#22C55E',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  navIcon: {
    fontSize: 22,
    marginBottom: 6,
  },
  navLabel: {
    fontSize: 12,
    color: '#4a5568',
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  navLabelActive: {
    color: '#ffffff',
  },
  // Screen Headers
  screenHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    backgroundColor: '#ffffff',
  },
  screenTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  backButton: {
    fontSize: 16,
    color: '#059669',
    fontWeight: '500',
  },
  addButton: {
    fontSize: 16,
    color: '#059669',
    fontWeight: '500',
  },
  screenContent: {
    flex: 1,
    backgroundColor: '#F0FDF4',
  },
  // Empty States
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  // Journal Styles
  journalCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  journalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  journalDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  moodDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  moodText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  symptomsContainer: {
    marginBottom: 16,
  },
  symptomsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  symptomsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  symptomTag: {
    backgroundColor: '#eff6ff',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  symptomText: {
    fontSize: 12,
    color: '#2563eb',
    fontWeight: '500',
  },
  notesContainer: {
    marginTop: 8,
  },
  notesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  modalCancel: {
    fontSize: 16,
    color: '#6b7280',
  },
  modalSave: {
    fontSize: 16,
    color: '#2563eb',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  symptomButton: {
    backgroundColor: '#f3f4f6',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  symptomButtonSelected: {
    backgroundColor: '#2563eb',
  },
  symptomButtonText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  symptomButtonTextSelected: {
    color: '#ffffff',
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
    backgroundColor: '#ffffff',
  },
  // Enhanced Community Styles
  communityScreenHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#F0FDF4',
    borderBottomWidth: 1,
    borderBottomColor: '#D1FAE5',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  crisisButton: {
    backgroundColor: '#FEF2F2',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  crisisButtonText: {
    fontSize: 16,
    color: '#DC2626',
  },
  addPostButton: {
    backgroundColor: '#22C55E',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  safetyBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#D1FAE5',
  },
  safetyIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  safetyTextContainer: {
    flex: 1,
  },
  safetyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#059669',
    marginBottom: 2,
  },
  safetySubtext: {
    fontSize: 12,
    color: '#6B7280',
  },
  communityNavTabs: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  communityNavTab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginHorizontal: 2,
  },
  communityNavTabActive: {
    backgroundColor: '#F0FDF4',
    borderWidth: 1,
    borderColor: '#22C55E',
  },
  communityNavIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  communityNavText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
  },
  communityNavTextActive: {
    color: '#059669',
    fontWeight: '600',
  },
  communityStats: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: '#059669',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  communityHeader: {
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  communityWelcome: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  communitySubtext: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  categoryTabs: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
    backgroundColor: '#f3f4f6',
  },
  categoryTabActive: {
    backgroundColor: '#2563eb',
  },
  categoryTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  // Post Styles
  postCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  postAuthor: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
  authorName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  postTime: {
    fontSize: 12,
    color: '#6b7280',
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f3f4f6',
  },
  categoryBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
  },
  categorysupport: {
    backgroundColor: '#dcfce7',
  },
  categoryquestion: {
    backgroundColor: '#fef3c7',
  },
  categorysuccess: {
    backgroundColor: '#dbeafe',
  },
  categoryresource: {
    backgroundColor: '#f3e8ff',
  },
  postContent: {
    fontSize: 14,
    color: '#1f2937',
    lineHeight: 20,
    marginBottom: 16,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 12,
  },
  postAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  postActionIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  postActionText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },

  // Enhanced Community Styles
  sectionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#F0FDF4',
    borderBottomWidth: 1,
    borderBottomColor: '#D1FAE5',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#059669',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },

  // Enhanced Post Card Styles
  enhancedPostCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  authorInfo: {
    flex: 1,
  },
  authorNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  verifiedBadge: {
    fontSize: 12,
    color: '#22C55E',
    marginLeft: 4,
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 8,
  },
  professionalBadge: {
    fontSize: 10,
    marginLeft: 4,
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 8,
  },
  locationText: {
    fontSize: 11,
    color: '#6B7280',
    marginTop: 2,
  },
  anonymousAvatar: {
    backgroundColor: '#F3F4F6',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  reportButton: {
    fontSize: 18,
    color: '#9CA3AF',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  moodIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginVertical: 8,
    alignSelf: 'flex-start',
  },
  moodpositive: {
    backgroundColor: '#FEF3C7',
  },
  'moodseeking-help': {
    backgroundColor: '#FEF2F2',
  },
  moodsupportive: {
    backgroundColor: '#F0FDF4',
  },
  moodencouraging: {
    backgroundColor: '#EFF6FF',
  },
  moodgrateful: {
    backgroundColor: '#F5F3FF',
  },
  moodnurturing: {
    backgroundColor: '#FDF2F8',
  },
  moodText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 8,
    gap: 6,
  },
  tag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 11,
    color: '#6B7280',
    fontWeight: '500',
  },
  actionIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  supportActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    backgroundColor: '#F0FDF4',
    borderWidth: 1,
    borderColor: '#22C55E',
  },
  supportActionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#059669',
  },
  shareButton: {
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 12,
    backgroundColor: '#F8FAFC',
  },

  // Support Groups Styles
  groupCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  groupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  groupDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  groupMeta: {
    gap: 4,
  },
  groupMembers: {
    fontSize: 12,
    color: '#059669',
    fontWeight: '500',
  },
  groupMeeting: {
    fontSize: 12,
    color: '#6B7280',
  },
  groupModerator: {
    fontSize: 12,
    color: '#6B7280',
  },
  groupPrivacy: {
    alignItems: 'flex-end',
  },
  privacyBadge: {
    fontSize: 10,
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    color: '#6B7280',
  },
  groupTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 8,
    gap: 6,
  },
  groupTag: {
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  groupTagText: {
    fontSize: 10,
    color: '#2563EB',
    fontWeight: '500',
  },
  joinGroupButton: {
    backgroundColor: '#22C55E',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  joinGroupText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },

  // Mentor Styles
  mentorCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  mentorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  mentorAvatar: {
    position: 'relative',
    marginRight: 12,
  },
  mentorAvatarText: {
    fontSize: 32,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#9CA3AF',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  onlineActive: {
    backgroundColor: '#22C55E',
  },
  mentorInfo: {
    flex: 1,
  },
  mentorNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  mentorName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  mentorExperience: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  mentorRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingStars: {
    fontSize: 12,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  mentorSpecialties: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 8,
    gap: 6,
  },
  specialtyTag: {
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#22C55E',
  },
  specialtyText: {
    fontSize: 10,
    color: '#059669',
    fontWeight: '500',
  },
  mentorDetails: {
    marginVertical: 8,
    gap: 4,
  },
  responseTime: {
    fontSize: 12,
    color: '#6B7280',
  },
  languages: {
    fontSize: 12,
    color: '#6B7280',
  },
  connectButton: {
    backgroundColor: '#22C55E',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  connectButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },

  // Event Styles
  eventCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  eventBadge: {
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 10,
    color: '#2563EB',
    fontWeight: '500',
  },
  eventDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  eventDetails: {
    marginBottom: 12,
    gap: 4,
  },
  eventTime: {
    fontSize: 12,
    color: '#059669',
    fontWeight: '500',
  },
  eventLocation: {
    fontSize: 12,
    color: '#6B7280',
  },
  eventHost: {
    fontSize: 12,
    color: '#6B7280',
  },
  joinEventButton: {
    backgroundColor: '#2563EB',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  joinEventText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },

  // Resources Styles
  resourcesHeader: {
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  resourcesWelcome: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  resourcesSubtext: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  resourceCategories: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  resourceCategory: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    marginRight: '2%',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  resourceCategoryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  resourceCategoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#1f2937',
    textAlign: 'center',
  },
  resourceCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  resourceHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  resourceIcon: {
    fontSize: 24,
    marginRight: 12,
    marginTop: 4,
  },
  resourceInfo: {
    flex: 1,
  },
  resourceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  resourceDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  resourceButton: {
    backgroundColor: '#2563eb',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  resourceButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 14,
  },
  // Emergency Modal Styles
  emergencyModalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  emergencyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#dc2626',
  },
  emergencyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#ffffff',
  },
  emergencyClose: {
    fontSize: 24,
    color: '#ffffff',
    fontWeight: '600',
  },
  emergencyContent: {
    flex: 1,
    padding: 20,
  },
  emergencySection: {
    marginBottom: 24,
  },
  emergencyMessage: {
    fontSize: 16,
    color: '#1f2937',
    lineHeight: 24,
    textAlign: 'center',
    backgroundColor: '#fef2f2',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  emergencyContacts: {
    marginBottom: 24,
  },
  emergencyContactsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  emergencyContact: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  emergencyContactIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  emergencyContactInfo: {
    flex: 1,
  },
  emergencyContactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 2,
  },
  emergencyContactNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#dc2626',
    marginBottom: 2,
  },
  emergencyContactDesc: {
    fontSize: 12,
    color: '#6b7280',
  },
  emergencyNote: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    textAlign: 'center',
    fontStyle: 'italic',
    backgroundColor: '#f8fafc',
    padding: 16,
    borderRadius: 12,
  },
  // Did You Know Modal Styles
  factCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  factHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  factEmoji: {
    fontSize: 48,
    marginRight: 16,
  },
  factMeta: {
    flex: 1,
  },
  factCategory: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2563eb',
    marginBottom: 4,
  },
  funLevel: {
    fontSize: 12,
    color: '#6b7280',
  },
  factText: {
    fontSize: 18,
    color: '#1f2937',
    lineHeight: 26,
    marginBottom: 20,
    fontWeight: '500',
  },
  actionTipContainer: {
    backgroundColor: '#f0fdf4',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#22c55e',
  },
  actionTipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#15803d',
    marginBottom: 8,
  },
  actionTipText: {
    fontSize: 14,
    color: '#166534',
    lineHeight: 20,
  },
  factSource: {
    fontSize: 12,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  moreFactsButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    padding: 16,
    margin: 16,
    alignItems: 'center',
  },
  moreFactsButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  shareFactButton: {
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  shareFactButtonText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '500',
  },
  // Quiz Styles
  featuredSection: {
    margin: 16,
  },
  featuredTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  featuredQuiz: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  quizHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  quizIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  quizInfo: {
    flex: 1,
  },
  quizTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  quizDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  quizMeta: {
    fontSize: 12,
    color: '#9ca3af',
  },
  quizCta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#2563eb',
    borderRadius: 12,
    padding: 12,
  },
  quizCtaText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  quizCtaIcon: {
    fontSize: 16,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d5db',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#2563eb',
    borderColor: '#2563eb',
  },
  emergencyButton: {
    backgroundColor: '#dc2626',
    borderColor: '#dc2626',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  emergencyButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  // Enhanced Chat Styles
  chatContainer: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  backButtonContainer: {
    padding: 8,
  },
  backButton: {
    fontSize: 16,
    color: '#2563eb',
    fontWeight: '600',
  },
  chatHeaderCenter: {
    flex: 1,
    alignItems: 'center',
  },
  chatTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  chatSubtitle: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  newChatButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
  },
  newChatIcon: {
    fontSize: 20,
    color: 'white',
    fontWeight: '600',
  },
  messagesContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 20,
  },
  messageContainer: {
    marginBottom: 16,
    maxWidth: '85%',
  },
  userMessage: {
    alignSelf: 'flex-end',
  },
  aiMessage: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: 20,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  userMessageBubble: {
    backgroundColor: '#2563eb',
    borderBottomRightRadius: 6,
  },
  aiMessageBubble: {
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 6,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  errorMessageBubble: {
    backgroundColor: '#fef2f2',
    borderColor: '#fecaca',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: 'white',
  },
  aiMessageText: {
    color: '#1f2937',
  },
  errorMessageText: {
    color: '#dc2626',
  },
  messageMetadata: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  messageTime: {
    fontSize: 11,
    fontWeight: '500',
  },
  userMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  aiMessageTime: {
    color: '#6b7280',
  },
  deliveryStatus: {
    fontSize: 12,
    marginLeft: 4,
  },
  confidenceScore: {
    fontSize: 10,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  emergencyIndicatorContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  emergencyIndicator: {
    fontSize: 14,
    color: '#dc2626',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  emergencyButton: {
    backgroundColor: '#dc2626',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  emergencyButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  topicsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    gap: 6,
  },
  topicTag: {
    backgroundColor: '#eff6ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#bfdbfe',
  },
  topicText: {
    fontSize: 12,
    color: '#2563eb',
    fontWeight: '500',
  },
  recommendationsContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#f0fdf4',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#166534',
    marginBottom: 6,
  },
  recommendationText: {
    fontSize: 13,
    color: '#166534',
    lineHeight: 18,
    marginBottom: 2,
  },
  typingBubble: {
    minHeight: 60,
    justifyContent: 'center',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#6b7280',
    marginHorizontal: 2,
  },
  typingDot1: {
    // Animation would be added with Animated API
  },
  typingDot2: {
    // Animation would be added with Animated API
  },
  typingDot3: {
    // Animation would be added with Animated API
  },
  typingText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    alignItems: 'flex-end',
    backgroundColor: '#ffffff',
  },
  inputWrapper: {
    flex: 1,
    marginRight: 12,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 120,
    fontSize: 16,
    backgroundColor: '#f9fafb',
  },
  characterCount: {
    fontSize: 11,
    color: '#6b7280',
    textAlign: 'right',
    marginTop: 4,
    marginRight: 8,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#2563eb',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  sendButtonDisabled: {
    backgroundColor: '#9ca3af',
    shadowOpacity: 0,
    elevation: 0,
  },
  sendButtonText: {
    fontSize: 20,
    color: 'white',
    fontWeight: '600',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    gap: 8,
  },
  quickActionButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    alignItems: 'center',
  },
  quickActionText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },

  // Enhanced Journal Styles
  journalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    backgroundColor: '#ffffff',
  },
  journalHeaderCenter: {
    flex: 1,
    alignItems: 'center',
  },
  journalSubtitle: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  journalHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  analyticsButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  analyticsIcon: {
    fontSize: 16,
  },
  addEntryButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addEntryIcon: {
    fontSize: 20,
    color: 'white',
    fontWeight: '600',
  },
  analyticsPanel: {
    backgroundColor: '#f8fafc',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  analyticsPanelTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  analyticsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  analyticsCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 4,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  analyticsValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2563eb',
  },
  analyticsLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  analyticsEmoji: {
    fontSize: 16,
    marginTop: 4,
  },
  symptomsAnalytics: {
    marginTop: 8,
  },
  symptomsAnalyticsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  symptomsAnalyticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  symptomAnalyticsTag: {
    backgroundColor: '#fef3c7',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  symptomAnalyticsText: {
    fontSize: 12,
    color: '#92400e',
    fontWeight: '500',
  },
  symptomAnalyticsCount: {
    fontSize: 10,
    color: '#92400e',
    fontWeight: '700',
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  filterTab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
    marginHorizontal: 2,
  },
  filterTabActive: {
    backgroundColor: '#eff6ff',
    borderWidth: 1,
    borderColor: '#bfdbfe',
  },
  filterTabIcon: {
    fontSize: 16,
    marginBottom: 2,
  },
  filterTabText: {
    fontSize: 10,
    color: '#6b7280',
    fontWeight: '500',
    textAlign: 'center',
  },
  filterTabTextActive: {
    color: '#2563eb',
    fontWeight: '600',
  },
  entriesContainer: {
    paddingBottom: 20,
  },
  enhancedJournalCard: {
    margin: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  journalCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  journalDateContainer: {
    flex: 1,
  },
  journalTime: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  moodDisplayLarge: {
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 12,
    minWidth: 80,
  },
  moodEmojiLarge: {
    fontSize: 32,
    marginBottom: 4,
  },
  moodTextLarge: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
  },
  moodRating: {
    flexDirection: 'row',
    marginTop: 4,
  },
  moodStar: {
    fontSize: 8,
    color: '#d1d5db',
  },
  moodStarActive: {
    color: '#fbbf24',
  },
  symptomsSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  symptomTagEnhanced: {
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 6,
    marginBottom: 6,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  symptomTextEnhanced: {
    fontSize: 12,
    color: '#dc2626',
    fontWeight: '500',
  },
  notesSection: {
    marginBottom: 16,
  },
  notesTextEnhanced: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  entryActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  entryAction: {
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  entryActionIcon: {
    fontSize: 16,
    marginBottom: 4,
  },
  entryActionText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },

  // Enhanced Modal Styles
  enhancedModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    backgroundColor: '#ffffff',
  },
  modalCancelButton: {
    padding: 8,
  },
  modalTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  modalSubtitle: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  modalSaveButton: {
    backgroundColor: '#2563eb',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  enhancedModalSection: {
    marginBottom: 24,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  sectionHeaderContainer: {
    marginBottom: 16,
  },
  sectionDescription: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  enhancedMoodSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  enhancedMoodButton: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 16,
    flex: 1,
    marginHorizontal: 2,
    backgroundColor: '#f9fafb',
    borderWidth: 2,
    borderColor: '#e5e7eb',
  },
  enhancedMoodButtonSelected: {
    backgroundColor: '#eff6ff',
    borderColor: '#2563eb',
    transform: [{ scale: 1.05 }],
  },
  moodEmojiSelected: {
    transform: [{ scale: 1.2 }],
  },
  moodLabelEnhanced: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 4,
  },
  moodLabelSelected: {
    color: '#2563eb',
    fontWeight: '600',
  },
  moodRatingContainer: {
    flexDirection: 'row',
    marginTop: 4,
  },
  moodStarSmall: {
    fontSize: 8,
    color: '#d1d5db',
  },
  moodStarActiveSmall: {
    color: '#fbbf24',
  },
  enhancedSymptomsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  enhancedSymptomButton: {
    backgroundColor: '#f9fafb',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  enhancedSymptomButtonSelected: {
    backgroundColor: '#fef2f2',
    borderColor: '#fecaca',
  },
  enhancedSymptomButtonText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  enhancedSymptomButtonTextSelected: {
    color: '#dc2626',
    fontWeight: '600',
  },
  symptomCheckmark: {
    fontSize: 10,
    color: '#dc2626',
    fontWeight: '700',
  },
  metricsContainer: {
    gap: 12,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  metricLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
    flex: 1,
  },
  metricCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  metricButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  metricButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    minWidth: 24,
    textAlign: 'center',
  },
  notesInputContainer: {
    position: 'relative',
  },
  enhancedNotesInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 12,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#f9fafb',
    textAlignVertical: 'top',
    minHeight: 120,
  },
  characterCountModal: {
    fontSize: 11,
    color: '#6b7280',
    textAlign: 'right',
    marginTop: 4,
  },
  quickPromptsContainer: {
    gap: 8,
    marginTop: 12,
  },
  quickPromptButton: {
    backgroundColor: '#f0fdf4',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  quickPromptText: {
    fontSize: 13,
    color: '#166534',
    fontWeight: '500',
  },
  modalFooter: {
    paddingTop: 16,
    paddingBottom: 32,
  },
  saveEntryButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#2563eb',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  saveEntryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },

  // Appointments Screen Styles
  appointmentsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  appointmentsHeaderCenter: {
    flex: 1,
    alignItems: 'center',
  },
  appointmentsSubtitle: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  addAppointmentButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#22C55E',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  addAppointmentIcon: {
    fontSize: 20,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  appointmentFilters: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginHorizontal: 4,
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
  },
  filterTabActive: {
    backgroundColor: '#22C55E',
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  filterTabTextActive: {
    color: '#ffffff',
  },
  appointmentsContainer: {
    padding: 16,
  },
  appointmentCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  appointmentCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  appointmentTitleContainer: {
    flex: 1,
    marginRight: 12,
  },
  appointmentTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  appointmentMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appointmentProvider: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  appointmentType: {
    fontSize: 14,
    color: '#9CA3AF',
    marginLeft: 4,
  },
  appointmentStatusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  statusIcon: {
    fontSize: 12,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
    textTransform: 'capitalize',
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#ffffff',
  },
  appointmentDetails: {
    marginBottom: 16,
  },
  appointmentDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  appointmentDetailIcon: {
    fontSize: 16,
    marginRight: 8,
    width: 20,
  },
  appointmentDetailText: {
    fontSize: 14,
    color: '#4B5563',
    flex: 1,
  },
  appointmentActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  quickActionButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
  },
  emptyActionButton: {
    backgroundColor: '#22C55E',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginTop: 16,
  },
  emptyActionText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },

  // Appointment Booking Modal Styles
  stepIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepCircleActive: {
    backgroundColor: '#22C55E',
  },
  stepCircleCompleted: {
    backgroundColor: '#10B981',
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  stepNumberActive: {
    color: '#ffffff',
  },
  stepLine: {
    width: 24,
    height: 2,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 8,
  },
  bookingStep: {
    padding: 20,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
  },
  appointmentTypesGrid: {
    gap: 16,
  },
  appointmentTypeCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  appointmentTypeCardSelected: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  appointmentTypeIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  appointmentTypeName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  appointmentTypeDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  appointmentTypeDuration: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  providersContainer: {
    gap: 12,
  },
  providerCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  providerCardSelected: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  providerInfo: {
    gap: 4,
  },
  providerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  providerSpecialty: {
    fontSize: 14,
    color: '#6B7280',
  },
  providerLocation: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  providerRating: {
    marginTop: 4,
  },
  providerRatingText: {
    fontSize: 12,
    color: '#F59E0B',
    fontWeight: '500',
  },
  dateSelection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  dateScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  dateOption: {
    alignItems: 'center',
    padding: 12,
    marginRight: 12,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    minWidth: 60,
  },
  dateOptionSelected: {
    backgroundColor: '#22C55E',
  },
  dateOptionDay: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  dateOptionDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 2,
  },
  dateOptionTextSelected: {
    color: '#ffffff',
  },
  timeSelection: {
    marginBottom: 24,
  },
  timeSlotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  timeSlot: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  timeSlotSelected: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  timeSlotUnavailable: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
    opacity: 0.5,
  },
  timeSlotText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  timeSlotTextSelected: {
    color: '#ffffff',
  },
  timeSlotTextUnavailable: {
    color: '#9CA3AF',
  },
  stepNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
    gap: 16,
  },
  backStepButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
  },
  backStepButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  nextStepButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: '#22C55E',
    alignItems: 'center',
  },
  nextStepButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  urgencySection: {
    marginBottom: 24,
  },
  urgencyOptions: {
    flexDirection: 'row',
    gap: 12,
  },
  urgencyOption: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    backgroundColor: '#ffffff',
  },
  urgencyOptionSelected: {
    backgroundColor: '#F0FDF4',
  },
  urgencyIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  urgencyLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  notesSection: {
    marginBottom: 24,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#ffffff',
    minHeight: 100,
  },
  confirmationCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 24,
  },
  confirmationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  confirmationTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    flex: 1,
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  urgencyBadgeText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#ffffff',
  },
  confirmationDetails: {
    gap: 12,
  },
  confirmationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  confirmationLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
    flex: 1,
  },
  confirmationValue: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '600',
    flex: 2,
    textAlign: 'right',
  },
  confirmBookingButton: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: '#22C55E',
    alignItems: 'center',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  confirmBookingButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#ffffff',
  },

  // Appointment Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#F0FDF4',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalCloseButton: {
    fontSize: 24,
    color: '#6B7280',
    fontWeight: '600',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
  },
  modalHeaderSpacer: {
    width: 24,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  appointmentDetailHeader: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  appointmentDetailTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  statusIcon: {
    fontSize: 16,
    marginRight: 6,
    color: '#ffffff',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
    textTransform: 'capitalize',
  },
  appointmentDetailSection: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
    flex: 1,
  },
  detailValue: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '600',
    flex: 2,
    textAlign: 'right',
  },
  queueInfoCard: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
  },
  queueInfoItem: {
    alignItems: 'center',
  },
  queueInfoLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  queueInfoValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#059669',
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checklistIcon: {
    fontSize: 16,
    color: '#22C55E',
    marginRight: 12,
    fontWeight: '700',
  },
  checklistText: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  notesText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginTop: 8,
  },
  appointmentDetailActions: {
    gap: 12,
    marginTop: 20,
  },
  actionButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  confirmButton: {
    backgroundColor: '#22C55E',
  },
  checkInButton: {
    backgroundColor: '#3B82F6',
  },
  rescheduleButton: {
    backgroundColor: '#F59E0B',
  },
  cancelButton: {
    backgroundColor: '#EF4444',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
  },

  // Booking Modal Styles
  formSection: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#ffffff',
  },
  formTextArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  typeOption: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  typeOptionSelected: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  typeIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  typeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  typeTextSelected: {
    color: '#059669',
  },
  providerScroll: {
    marginTop: 8,
  },
  providerCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    minWidth: 200,
  },
  providerCardSelected: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  providerName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  providerSpecialty: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  providerRating: {
    fontSize: 14,
    color: '#F59E0B',
    fontWeight: '600',
    marginBottom: 4,
  },
  providerLocation: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  timeSlotGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  timeSlot: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  timeSlotSelected: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  timeSlotText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  timeSlotTextSelected: {
    color: '#ffffff',
  },
  bookButton: {
    backgroundColor: '#22C55E',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  bookButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#ffffff',
  },

  // Enhanced Journal Modal Styles
  floatingActionButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#22C55E',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  floatingActionButtonText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#ffffff',
  },
  moodSelectorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 8,
  },
  moodSelectorButton: {
    flex: 1,
    minWidth: '30%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  moodSelectorButtonSelected: {
    borderColor: '#22C55E',
    backgroundColor: '#F0FDF4',
  },
  moodSelectorEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  moodSelectorLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  moodSelectorLabelSelected: {
    color: '#059669',
  },
  moodRatingStars: {
    flexDirection: 'row',
    gap: 2,
  },
  moodStar: {
    fontSize: 12,
    color: '#D1D5DB',
  },
  moodStarActive: {
    color: '#F59E0B',
  },
  symptomsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  symptomChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  symptomChipSelected: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  symptomChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  symptomChipTextSelected: {
    color: '#ffffff',
  },
  symptomCheckmark: {
    fontSize: 12,
    color: '#ffffff',
    marginLeft: 4,
    fontWeight: '700',
  },
  metricsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  metricItem: {
    flex: 1,
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
    textAlign: 'center',
  },
  metricInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    textAlign: 'center',
    minWidth: 80,
  },
  levelSelectors: {
    gap: 16,
    marginTop: 16,
  },
  levelSelector: {
    alignItems: 'center',
  },
  levelLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  levelButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  levelButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#D1D5DB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  levelButtonSelected: {
    backgroundColor: '#22C55E',
    borderColor: '#22C55E',
  },
  levelButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#374151',
  },
  levelButtonTextSelected: {
    color: '#ffffff',
  },
  characterCount: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
    marginTop: 4,
  },
  saveJournalButton: {
    backgroundColor: '#22C55E',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  saveJournalButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#ffffff',
  },
});

export default App;
